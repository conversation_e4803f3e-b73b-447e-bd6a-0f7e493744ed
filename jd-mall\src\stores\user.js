import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('user_token') || '')
  const isLoading = ref(false)
  const loginHistory = ref([])

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userName = computed(() => user.value?.name || '未登录')
  const userAvatar = computed(() => user.value?.avatar || '/default-avatar.png')
  const userLevel = computed(() => user.value?.level || 'bronze')

  // 动作
  const login = async (credentials) => {
    isLoading.value = true
    try {
      // 模拟登录API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟用户数据
      const userData = {
        id: Date.now(),
        name: credentials.username || credentials.phone,
        email: credentials.email || `${credentials.username}@example.com`,
        phone: credentials.phone || '138****8888',
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${credentials.username}`,
        level: 'gold',
        points: 1580,
        balance: 299.50,
        addresses: [
          {
            id: 1,
            name: '张三',
            phone: '13888888888',
            province: '北京市',
            city: '北京市',
            district: '朝阳区',
            detail: '三里屯街道工体北路8号院',
            isDefault: true
          }
        ],
        preferences: {
          notifications: true,
          newsletter: false,
          sms: true
        }
      }
      
      user.value = userData
      token.value = `token_${userData.id}_${Date.now()}`
      
      // 保存到本地存储
      localStorage.setItem('user_token', token.value)
      localStorage.setItem('user_info', JSON.stringify(userData))
      
      // 记录登录历史
      loginHistory.value.unshift({
        time: new Date().toISOString(),
        ip: '***********',
        device: navigator.userAgent
      })
      
      return { success: true, user: userData }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    isLoading.value = true
    try {
      // 模拟注册API调用
      await new Promise(resolve => setTimeout(resolve, 1200))
      
      // 注册成功后自动登录
      return await login({
        username: userData.username,
        password: userData.password
      })
    } catch (error) {
      console.error('注册失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    user.value = null
    token.value = ''
    localStorage.removeItem('user_token')
    localStorage.removeItem('user_info')
    
    // 清除其他相关数据
    const cartStore = useCartStore()
    cartStore.clearCart()
  }

  const updateProfile = async (profileData) => {
    isLoading.value = true
    try {
      // 模拟更新API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      user.value = { ...user.value, ...profileData }
      localStorage.setItem('user_info', JSON.stringify(user.value))
      
      return { success: true }
    } catch (error) {
      console.error('更新资料失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const addAddress = async (addressData) => {
    try {
      const newAddress = {
        id: Date.now(),
        ...addressData,
        isDefault: user.value.addresses.length === 0
      }
      
      user.value.addresses.push(newAddress)
      localStorage.setItem('user_info', JSON.stringify(user.value))
      
      return { success: true, address: newAddress }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const updateAddress = async (addressId, addressData) => {
    try {
      const index = user.value.addresses.findIndex(addr => addr.id === addressId)
      if (index !== -1) {
        user.value.addresses[index] = { ...user.value.addresses[index], ...addressData }
        localStorage.setItem('user_info', JSON.stringify(user.value))
      }
      
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const deleteAddress = async (addressId) => {
    try {
      user.value.addresses = user.value.addresses.filter(addr => addr.id !== addressId)
      localStorage.setItem('user_info', JSON.stringify(user.value))
      
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const setDefaultAddress = async (addressId) => {
    try {
      user.value.addresses.forEach(addr => {
        addr.isDefault = addr.id === addressId
      })
      localStorage.setItem('user_info', JSON.stringify(user.value))
      
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  // 初始化用户数据
  const initUser = () => {
    const savedUser = localStorage.getItem('user_info')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户数据失败:', error)
        logout()
      }
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    loginHistory,
    
    // 计算属性
    isAuthenticated,
    userName,
    userAvatar,
    userLevel,
    
    // 动作
    login,
    register,
    logout,
    updateProfile,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    initUser
  }
})

// 避免循环依赖，在这里导入
let useCartStore
if (typeof window !== 'undefined') {
  import('./cart.js').then(module => {
    useCartStore = module.useCartStore
  })
}
