<template>
  <div class="carousel" @mouseenter="pauseAutoPlay" @mouseleave="startAutoPlay">
    <div class="carousel-container">
      <!-- 轮播内容 -->
      <div 
        class="carousel-wrapper" 
        :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
      >
        <div 
          class="carousel-slide" 
          v-for="(item, index) in items" 
          :key="index"
        >
          <a :href="item.link" class="slide-link">
            <img 
              :src="item.image" 
              :alt="item.title"
              class="slide-image"
              @error="handleImageError"
            />
            <div class="slide-content" v-if="item.title || item.description">
              <h3 class="slide-title" v-if="item.title">{{ item.title }}</h3>
              <p class="slide-description" v-if="item.description">{{ item.description }}</p>
            </div>
          </a>
        </div>
      </div>
      
      <!-- 导航箭头 -->
      <button 
        class="carousel-arrow carousel-arrow-prev" 
        @click="prevSlide"
        v-if="showArrows && items.length > 1"
      >
        <i class="arrow-icon">‹</i>
      </button>
      <button 
        class="carousel-arrow carousel-arrow-next" 
        @click="nextSlide"
        v-if="showArrows && items.length > 1"
      >
        <i class="arrow-icon">›</i>
      </button>
      
      <!-- 指示器 -->
      <div class="carousel-indicators" v-if="showIndicators && items.length > 1">
        <button
          class="indicator"
          v-for="(item, index) in items"
          :key="index"
          :class="{ active: index === currentIndex }"
          @click="goToSlide(index)"
        ></button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Carousel',
  props: {
    items: {
      type: Array,
      required: true,
      default: () => []
    },
    autoPlay: {
      type: Boolean,
      default: true
    },
    interval: {
      type: Number,
      default: 5000
    },
    showArrows: {
      type: Boolean,
      default: true
    },
    showIndicators: {
      type: Boolean,
      default: true
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  data() {
    return {
      currentIndex: 0,
      autoPlayTimer: null
    }
  },
  mounted() {
    if (this.autoPlay && this.items.length > 1) {
      this.startAutoPlay()
    }
  },
  beforeUnmount() {
    this.clearAutoPlay()
  },
  methods: {
    nextSlide() {
      this.currentIndex = (this.currentIndex + 1) % this.items.length
    },
    
    prevSlide() {
      this.currentIndex = this.currentIndex === 0 
        ? this.items.length - 1 
        : this.currentIndex - 1
    },
    
    goToSlide(index) {
      this.currentIndex = index
    },
    
    startAutoPlay() {
      if (this.autoPlay && this.items.length > 1) {
        this.autoPlayTimer = setInterval(() => {
          this.nextSlide()
        }, this.interval)
      }
    },
    
    pauseAutoPlay() {
      this.clearAutoPlay()
    },
    
    clearAutoPlay() {
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer)
        this.autoPlayTimer = null
      }
    },
    
    handleImageError(event) {
      // 图片加载失败时显示默认图片
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI0MDAiIHZpZXdCb3g9IjAgMCAxMjAwIDQwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik02MDAgMTUwQzU2NC4xMDEgMTUwIDUzNS43MTQgMTc4LjM4NyA1MzUuNzE0IDIxNC4yODZDNTM1LjcxNCAyNTAuMTg0IDU2NC4xMDEgMjc4LjU3MSA2MDAgMjc4LjU3MUM2MzUuODk5IDI3OC41NzEgNjY0LjI4NiAyNTAuMTg0IDY2NC4yODYgMjE0LjI4NkM2NjQuMjg2IDE3OC4zODcgNjM1Ljg5OSAxNTAgNjAwIDE1MFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPHBhdGggZD0iTTUwMCAzMDBINzAwVjI2MEg1MDBWMZAAWSIGZMLSPSJDQ0NDQ0MiLz4KPC9zdmc+'
    }
  }
}
</script>

<style lang="scss" scoped>
.carousel {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: $border-radius-base;
  
  .carousel-container {
    position: relative;
    height: v-bind(height);
    
    @include respond-to(xs) {
      height: 200px;
    }
  }
  
  .carousel-wrapper {
    display: flex;
    height: 100%;
    transition: transform $transition-slow $ease-in-out;
  }
  
  .carousel-slide {
    flex: 0 0 100%;
    height: 100%;
    
    .slide-link {
      display: block;
      width: 100%;
      height: 100%;
      position: relative;
      text-decoration: none;
      
      .slide-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }
      
      .slide-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        color: $bg-primary;
        padding: $spacing-xl $spacing-lg $spacing-lg;
        
        .slide-title {
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          margin-bottom: $spacing-sm;
          @include text-ellipsis;
        }
        
        .slide-description {
          font-size: $font-size-sm;
          opacity: 0.9;
          margin: 0;
          @include text-ellipsis-multiline(2);
        }
        
        @include respond-to(xs) {
          padding: $spacing-base;
          
          .slide-title {
            font-size: $font-size-lg;
          }
          
          .slide-description {
            font-size: $font-size-xs;
          }
        }
      }
    }
  }
  
  // 导航箭头
  .carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: $bg-primary;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: $border-radius-round;
    cursor: pointer;
    transition: all $transition-fast $ease-out;
    z-index: 2;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.7);
    }
    
    .arrow-icon {
      font-size: 24px;
      font-weight: bold;
      display: block;
      line-height: 1;
    }
    
    &.carousel-arrow-prev {
      left: $spacing-base;
    }
    
    &.carousel-arrow-next {
      right: $spacing-base;
    }
    
    @include respond-to(xs) {
      width: 32px;
      height: 32px;
      
      .arrow-icon {
        font-size: 18px;
      }
    }
  }
  
  // 指示器
  .carousel-indicators {
    position: absolute;
    bottom: $spacing-base;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: $spacing-xs;
    z-index: 2;
    
    .indicator {
      width: 12px;
      height: 12px;
      border-radius: $border-radius-round;
      border: none;
      background-color: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      transition: all $transition-fast $ease-out;
      
      &.active {
        background-color: $bg-primary;
        transform: scale(1.2);
      }
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.8);
      }
    }
    
    @include respond-to(xs) {
      bottom: $spacing-sm;
      
      .indicator {
        width: 8px;
        height: 8px;
      }
    }
  }
}

// 轮播图进入动画
.carousel-slide {
  opacity: 0;
  animation: slideIn 0.5s ease-out forwards;
}

@keyframes slideIn {
  to {
    opacity: 1;
  }
}
</style>
