<template>
  <div class="register-page">
    <div class="container">
      <div class="register-container">
        <div class="register-header">
          <h1 class="register-title">注册</h1>
          <p class="register-subtitle">创建您的账户，开始购物之旅</p>
        </div>

        <form class="register-form" @submit.prevent="handleRegister">
          <div class="form-group">
            <label>用户名</label>
            <input 
              type="text" 
              v-model="registerForm.username"
              placeholder="请输入用户名"
              required
            />
          </div>

          <div class="form-group">
            <label>手机号</label>
            <input 
              type="tel" 
              v-model="registerForm.phone"
              placeholder="请输入手机号"
              required
            />
          </div>

          <div class="form-group">
            <label>密码</label>
            <input 
              type="password" 
              v-model="registerForm.password"
              placeholder="请输入密码"
              required
            />
          </div>

          <div class="form-group">
            <label>确认密码</label>
            <input 
              type="password" 
              v-model="registerForm.confirmPassword"
              placeholder="请再次输入密码"
              required
            />
          </div>

          <div class="form-options">
            <label class="agree-terms">
              <input type="checkbox" v-model="registerForm.agree" required />
              <span>我已阅读并同意<a href="#">用户协议</a>和<a href="#">隐私政策</a></span>
            </label>
          </div>

          <button 
            type="submit" 
            class="btn btn-primary register-btn"
            :disabled="isLoading || !canRegister"
          >
            {{ isLoading ? '注册中...' : '注册' }}
          </button>
        </form>

        <div class="register-footer">
          <p>已有账户？<router-link to="/login">立即登录</router-link></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user.js'

export default {
  name: 'Register',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()

    const isLoading = ref(false)
    const registerForm = ref({
      username: '',
      phone: '',
      password: '',
      confirmPassword: '',
      agree: false
    })

    const canRegister = computed(() => {
      return registerForm.value.password === registerForm.value.confirmPassword &&
             registerForm.value.agree
    })

    const handleRegister = async () => {
      if (!canRegister.value) {
        alert('请检查输入信息')
        return
      }

      isLoading.value = true
      try {
        const result = await userStore.register(registerForm.value)
        if (result.success) {
          router.push('/')
        } else {
          alert('注册失败：' + result.error)
        }
      } catch (error) {
        console.error('注册失败:', error)
        alert('注册失败，请重试')
      } finally {
        isLoading.value = false
      }
    }

    return {
      isLoading,
      registerForm,
      canRegister,
      handleRegister
    }
  }
}
</script>

<style lang="scss" scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-light 0%, $secondary-light 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-6;
  
  .register-container {
    background-color: $bg-primary;
    border-radius: $border-radius-xl;
    padding: $spacing-8;
    box-shadow: $box-shadow-lg;
    width: 100%;
    max-width: 400px;
    
    .register-header {
      text-align: center;
      margin-bottom: $spacing-8;
      
      .register-title {
        font-size: $font-size-xxl;
        font-weight: $font-weight-bold;
        color: $text-primary;
        margin-bottom: $spacing-2;
      }
      
      .register-subtitle {
        color: $text-secondary;
        margin: 0;
      }
    }
    
    .register-form {
      .form-group {
        margin-bottom: $spacing-5;
        
        label {
          display: block;
          font-weight: $font-weight-semibold;
          color: $text-primary;
          margin-bottom: $spacing-2;
        }
        
        input {
          width: 100%;
          padding: $spacing-3 $spacing-4;
          border: 1px solid $border-medium;
          border-radius: $border-radius-base;
          font-size: $font-size-base;
          transition: border-color $transition-fast $ease-out;
          
          &:focus {
            outline: none;
            border-color: $primary-color;
            box-shadow: $box-shadow-focus;
          }
          
          &::placeholder {
            color: $text-placeholder;
          }
        }
      }
      
      .form-options {
        margin-bottom: $spacing-6;
        
        .agree-terms {
          display: flex;
          align-items: flex-start;
          gap: $spacing-2;
          cursor: pointer;
          
          input[type="checkbox"] {
            width: auto;
            margin-top: 2px;
          }
          
          span {
            color: $text-secondary;
            font-size: $font-size-sm;
            line-height: $line-height-relaxed;
            
            a {
              color: $primary-color;
              text-decoration: none;
              
              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
      
      .register-btn {
        width: 100%;
        padding: $spacing-3 0;
        font-size: $font-size-base;
        font-weight: $font-weight-semibold;
        border-radius: $border-radius-base;
      }
    }
    
    .register-footer {
      text-align: center;
      margin-top: $spacing-6;
      
      p {
        color: $text-secondary;
        margin: 0;
        
        a {
          color: $primary-color;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>
