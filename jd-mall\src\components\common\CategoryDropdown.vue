<template>
  <div class="category-dropdown" @mouseenter="showMenu = true" @mouseleave="showMenu = false">
    <button class="category-btn">
      <i class="menu-icon">☰</i>
      <span>全部商品分类</span>
      <i class="arrow-icon" :class="{ active: showMenu }">▼</i>
    </button>
    
    <!-- 分类下拉菜单 -->
    <div class="category-menu" v-if="showMenu">
      <div class="category-main">
        <!-- 左侧分类列表 -->
        <div class="category-list">
          <div 
            class="category-item"
            v-for="category in categories"
            :key="category.id"
            @mouseenter="activeCategory = category"
            @click="goToCategory(category.id)"
            :class="{ active: activeCategory?.id === category.id }"
          >
            <span class="category-icon">{{ category.icon }}</span>
            <span class="category-name">{{ category.name }}</span>
            <i class="arrow-right">›</i>
          </div>
        </div>
        
        <!-- 右侧子分类展示 -->
        <div class="subcategory-panel" v-if="activeCategory">
          <div class="subcategory-header">
            <h4>{{ activeCategory.name }}</h4>
          </div>
          <div class="subcategory-content">
            <div class="subcategory-section" v-if="activeCategory.children">
              <div class="subcategory-title">热门分类</div>
              <div class="subcategory-items">
                <a 
                  href="#" 
                  class="subcategory-item"
                  v-for="subcat in activeCategory.children.slice(0, 12)"
                  :key="subcat.id"
                  @click="goToCategory(activeCategory.id, subcat.id)"
                >
                  {{ subcat.name }}
                </a>
              </div>
            </div>
            
            <!-- 品牌推荐 -->
            <div class="brand-section" v-if="activeCategory.brands">
              <div class="brand-title">品牌推荐</div>
              <div class="brand-items">
                <a 
                  href="#" 
                  class="brand-item"
                  v-for="brand in activeCategory.brands.slice(0, 8)"
                  :key="brand.id"
                >
                  <img :src="brand.logo" :alt="brand.name" class="brand-logo" />
                  <span class="brand-name">{{ brand.name }}</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { useProductStore } from '../../stores/product.js'

export default {
  name: 'CategoryDropdown',
  setup() {
    const router = useRouter()
    const productStore = useProductStore()

    return {
      router,
      productStore
    }
  },
  data() {
    return {
      showMenu: false,
      activeCategory: null
    }
  },
  computed: {
    categories() {
      return this.productStore.categories
    }
  },
  methods: {
    goToCategory(categoryId, subcategoryId = null) {
      if (subcategoryId) {
        this.router.push(`/category/${categoryId}?sub=${subcategoryId}`)
      } else {
        this.router.push(`/category/${categoryId}`)
      }
      this.showMenu = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/_variables.scss";
@import "@/assets/scss/_mixins.scss";

.category-dropdown {
  position: relative;
  
  .category-btn {
    display: flex;
    align-items: center;
    gap: $spacing-2;
    padding: $spacing-3 $spacing-4;
    background-color: $primary-color;
    color: $text-white;
    border: none;
    border-radius: $border-radius-base 0 0 $border-radius-base;
    cursor: pointer;
    font-weight: $font-weight-semibold;
    white-space: nowrap;
    transition: background-color $transition-fast $ease-out;
    font-size: $font-size-sm;
    min-width: 160px;
    
    &:hover {
      background-color: $primary-hover;
    }
    
    .menu-icon {
      font-size: $font-size-base;
    }
    
    .arrow-icon {
      font-size: $font-size-xs;
      transition: transform $transition-fast $ease-out;
      margin-left: auto;
      
      &.active {
        transform: rotate(180deg);
      }
    }
    
    @include respond-to(md) {
      border-radius: $border-radius-base;
      margin-bottom: $spacing-2;
      min-width: auto;
    }
  }
  
  // 分类菜单
  .category-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: $bg-primary;
    border: 1px solid $border-light;
    border-radius: $border-radius-base;
    box-shadow: $box-shadow-lg;
    z-index: $z-index-dropdown;
    min-width: 800px;
    max-width: 1000px;

    @include respond-to(lg) {
      min-width: 600px;
    }

    @include respond-to(md) {
      display: none; // 在移动端隐藏下拉菜单
    }
    
    .category-main {
      display: flex;
      min-height: 400px;
      
      .category-list {
        width: 200px;
        border-right: 1px solid $border-light;
        background-color: $bg-tertiary;
        
        .category-item {
          display: flex;
          align-items: center;
          gap: $spacing-2;
          padding: $spacing-3 $spacing-4;
          cursor: pointer;
          transition: all $transition-fast $ease-out;
          border-bottom: 1px solid $border-light;
          
          &:hover,
          &.active {
            background-color: $bg-primary;
            color: $primary-color;
            border-right: 2px solid $primary-color;
          }
          
          .category-icon {
            font-size: $font-size-base;
            width: 20px;
            text-align: center;
          }
          
          .category-name {
            flex: 1;
            font-size: $font-size-sm;
            font-weight: $font-weight-medium;
          }
          
          .arrow-right {
            color: $text-tertiary;
            font-size: $font-size-sm;
          }
        }
      }
      
      .subcategory-panel {
        flex: 1;
        padding: $spacing-6;
        
        .subcategory-header {
          margin-bottom: $spacing-6;
          
          h4 {
            font-size: $font-size-lg;
            font-weight: $font-weight-bold;
            color: $text-primary;
            margin: 0;
          }
        }
        
        .subcategory-content {
          .subcategory-section {
            margin-bottom: $spacing-6;
            
            .subcategory-title {
              font-size: $font-size-base;
              font-weight: $font-weight-semibold;
              color: $text-primary;
              margin-bottom: $spacing-4;
              padding-bottom: $spacing-2;
              border-bottom: 1px solid $border-light;
            }
            
            .subcategory-items {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
              gap: $spacing-2;
              
              .subcategory-item {
                padding: $spacing-2 $spacing-3;
                color: $text-secondary;
                text-decoration: none;
                font-size: $font-size-sm;
                border-radius: $border-radius-base;
                transition: all $transition-fast $ease-out;
                text-align: center;
                border: 1px solid transparent;
                
                &:hover {
                  background-color: $primary-light;
                  color: $primary-color;
                  border-color: $primary-color;
                }
              }
            }
          }
          
          .brand-section {
            .brand-title {
              font-size: $font-size-base;
              font-weight: $font-weight-semibold;
              color: $text-primary;
              margin-bottom: $spacing-4;
              padding-bottom: $spacing-2;
              border-bottom: 1px solid $border-light;
            }
            
            .brand-items {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
              gap: $spacing-3;
              
              .brand-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: $spacing-2;
                padding: $spacing-3;
                color: $text-secondary;
                text-decoration: none;
                border-radius: $border-radius-base;
                transition: all $transition-fast $ease-out;
                border: 1px solid transparent;
                
                &:hover {
                  background-color: $bg-hover;
                  border-color: $border-medium;
                }
                
                .brand-logo {
                  width: 40px;
                  height: 40px;
                  object-fit: contain;
                  border-radius: $border-radius-sm;
                }
                
                .brand-name {
                  font-size: $font-size-xs;
                  text-align: center;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
