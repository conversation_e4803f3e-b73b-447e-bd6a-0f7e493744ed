import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCartStore = defineStore('cart', () => {
  // 状态
  const items = ref([])
  const isLoading = ref(false)

  // 计算属性
  const itemCount = computed(() => {
    return items.value.reduce((total, item) => total + item.quantity, 0)
  })

  const totalPrice = computed(() => {
    return items.value.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
  })

  const selectedItems = computed(() => {
    return items.value.filter(item => item.selected)
  })

  const selectedCount = computed(() => {
    return selectedItems.value.reduce((total, item) => total + item.quantity, 0)
  })

  const selectedTotalPrice = computed(() => {
    return selectedItems.value.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
  })

  const isEmpty = computed(() => items.value.length === 0)

  // 动作
  const addToCart = (product, quantity = 1, selectedSpec = null) => {
    const existingItemIndex = items.value.findIndex(item => 
      item.id === product.id && 
      JSON.stringify(item.selectedSpec) === JSON.stringify(selectedSpec)
    )

    if (existingItemIndex > -1) {
      // 如果商品已存在，增加数量
      items.value[existingItemIndex].quantity += quantity
    } else {
      // 添加新商品
      const cartItem = {
        id: product.id,
        title: product.title,
        price: product.price,
        originalPrice: product.originalPrice,
        image: product.image,
        quantity: quantity,
        selected: true,
        selectedSpec: selectedSpec,
        shop: product.shop,
        inStock: product.inStock,
        maxQuantity: product.maxQuantity || 99,
        addTime: Date.now()
      }
      items.value.push(cartItem)
    }

    // 保存到本地存储
    saveToStorage()
    
    return { success: true, message: '已添加到购物车' }
  }

  const removeFromCart = (itemId, selectedSpec = null) => {
    const index = items.value.findIndex(item => 
      item.id === itemId && 
      JSON.stringify(item.selectedSpec) === JSON.stringify(selectedSpec)
    )
    
    if (index > -1) {
      items.value.splice(index, 1)
      saveToStorage()
      return { success: true, message: '已从购物车移除' }
    }
    
    return { success: false, message: '商品不存在' }
  }

  const updateQuantity = (itemId, quantity, selectedSpec = null) => {
    const item = items.value.find(item => 
      item.id === itemId && 
      JSON.stringify(item.selectedSpec) === JSON.stringify(selectedSpec)
    )
    
    if (item) {
      if (quantity <= 0) {
        return removeFromCart(itemId, selectedSpec)
      }
      
      if (quantity > item.maxQuantity) {
        return { success: false, message: `最多只能购买${item.maxQuantity}件` }
      }
      
      item.quantity = quantity
      saveToStorage()
      return { success: true }
    }
    
    return { success: false, message: '商品不存在' }
  }

  const toggleItemSelection = (itemId, selectedSpec = null) => {
    const item = items.value.find(item => 
      item.id === itemId && 
      JSON.stringify(item.selectedSpec) === JSON.stringify(selectedSpec)
    )
    
    if (item) {
      item.selected = !item.selected
      saveToStorage()
      return { success: true }
    }
    
    return { success: false, message: '商品不存在' }
  }

  const selectAll = (selected = true) => {
    items.value.forEach(item => {
      item.selected = selected
    })
    saveToStorage()
  }

  const clearCart = () => {
    items.value = []
    saveToStorage()
  }

  const clearSelected = () => {
    items.value = items.value.filter(item => !item.selected)
    saveToStorage()
  }

  // 批量操作
  const batchUpdateQuantity = (updates) => {
    updates.forEach(update => {
      updateQuantity(update.itemId, update.quantity, update.selectedSpec)
    })
  }

  const batchRemove = (itemsToRemove) => {
    itemsToRemove.forEach(item => {
      removeFromCart(item.id, item.selectedSpec)
    })
  }

  // 获取商品在购物车中的数量
  const getItemQuantity = (productId, selectedSpec = null) => {
    const item = items.value.find(item => 
      item.id === productId && 
      JSON.stringify(item.selectedSpec) === JSON.stringify(selectedSpec)
    )
    return item ? item.quantity : 0
  }

  // 检查商品是否在购物车中
  const isInCart = (productId, selectedSpec = null) => {
    return items.value.some(item => 
      item.id === productId && 
      JSON.stringify(item.selectedSpec) === JSON.stringify(selectedSpec)
    )
  }

  // 保存到本地存储
  const saveToStorage = () => {
    try {
      localStorage.setItem('cart_items', JSON.stringify(items.value))
    } catch (error) {
      console.error('保存购物车数据失败:', error)
    }
  }

  // 从本地存储加载
  const loadFromStorage = () => {
    try {
      const savedItems = localStorage.getItem('cart_items')
      if (savedItems) {
        items.value = JSON.parse(savedItems)
      }
    } catch (error) {
      console.error('加载购物车数据失败:', error)
      items.value = []
    }
  }

  // 同步购物车数据（登录后）
  const syncCart = async () => {
    isLoading.value = true
    try {
      // 这里可以实现与服务器同步购物车的逻辑
      // 目前只是模拟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 合并本地和服务器的购物车数据
      // 实际项目中需要根据业务逻辑处理冲突
      
      return { success: true }
    } catch (error) {
      console.error('同步购物车失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  // 验证购物车商品（检查库存、价格变化等）
  const validateCart = async () => {
    isLoading.value = true
    try {
      // 模拟验证API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const invalidItems = []
      const updatedItems = []
      
      // 模拟一些验证结果
      items.value.forEach(item => {
        if (!item.inStock) {
          invalidItems.push({ ...item, reason: '商品已下架' })
        } else if (Math.random() > 0.9) {
          // 模拟价格变化
          const newPrice = item.price * (0.9 + Math.random() * 0.2)
          updatedItems.push({ 
            ...item, 
            oldPrice: item.price, 
            newPrice: newPrice,
            reason: '价格已更新'
          })
          item.price = newPrice
        }
      })
      
      if (invalidItems.length > 0 || updatedItems.length > 0) {
        saveToStorage()
      }
      
      return { 
        success: true, 
        invalidItems, 
        updatedItems 
      }
    } catch (error) {
      console.error('验证购物车失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    items,
    isLoading,
    
    // 计算属性
    itemCount,
    totalPrice,
    selectedItems,
    selectedCount,
    selectedTotalPrice,
    isEmpty,
    
    // 动作
    addToCart,
    removeFromCart,
    updateQuantity,
    toggleItemSelection,
    selectAll,
    clearCart,
    clearSelected,
    batchUpdateQuantity,
    batchRemove,
    getItemQuantity,
    isInCart,
    saveToStorage,
    loadFromStorage,
    syncCart,
    validateCart
  }
}, {
  persist: {
    key: 'jd-mall-cart',
    storage: localStorage,
    paths: ['items']
  }
})
