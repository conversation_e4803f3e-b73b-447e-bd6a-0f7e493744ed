<template>
  <div class="product-grid">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="skeleton-grid">
        <div 
          class="skeleton-card" 
          v-for="i in skeletonCount" 
          :key="i"
        >
          <div class="skeleton skeleton-image"></div>
          <div class="skeleton skeleton-title"></div>
          <div class="skeleton skeleton-price"></div>
          <div class="skeleton skeleton-button"></div>
        </div>
      </div>
    </div>
    
    <!-- 商品列表 -->
    <div v-else-if="products.length" class="grid-container">
      <ProductCard
        v-for="product in products"
        :key="product.id"
        :product="product"
        @click="handleProductClick"
        @add-to-cart="handleAddToCart"
      />
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📦</div>
      <h3 class="empty-title">暂无商品</h3>
      <p class="empty-description">{{ emptyText || '没有找到相关商品，试试其他关键词吧' }}</p>
    </div>
  </div>
</template>

<script>
import ProductCard from './ProductCard.vue'

export default {
  name: 'ProductGrid',
  components: {
    ProductCard
  },
  props: {
    products: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    emptyText: {
      type: String,
      default: ''
    },
    columns: {
      type: Object,
      default: () => ({
        xs: 2,
        sm: 3,
        md: 4,
        lg: 5,
        xl: 6
      })
    }
  },
  emits: ['product-click', 'add-to-cart'],
  computed: {
    skeletonCount() {
      // 根据屏幕大小显示不同数量的骨架屏
      return 12
    }
  },
  methods: {
    handleProductClick(product) {
      this.$emit('product-click', product)
    },
    
    handleAddToCart(product) {
      this.$emit('add-to-cart', product)
    }
  }
}
</script>

<style lang="scss" scoped>
.product-grid {
  width: 100%;
  
  // 网格容器
  .grid-container {
    display: grid;
    gap: $spacing-lg;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    
    // 响应式网格
    @include respond-to(xl) {
      grid-template-columns: repeat(6, 1fr);
    }
    
    @include respond-to(lg) {
      grid-template-columns: repeat(5, 1fr);
    }
    
    @include respond-to(md) {
      grid-template-columns: repeat(4, 1fr);
    }
    
    @include respond-to(sm) {
      grid-template-columns: repeat(3, 1fr);
      gap: $spacing-base;
    }
    
    @include respond-to(xs) {
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-sm;
    }
  }
  
  // 加载状态
  .loading-state {
    .skeleton-grid {
      display: grid;
      gap: $spacing-lg;
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      
      @include respond-to(xl) {
        grid-template-columns: repeat(6, 1fr);
      }
      
      @include respond-to(lg) {
        grid-template-columns: repeat(5, 1fr);
      }
      
      @include respond-to(md) {
        grid-template-columns: repeat(4, 1fr);
      }
      
      @include respond-to(sm) {
        grid-template-columns: repeat(3, 1fr);
        gap: $spacing-base;
      }
      
      @include respond-to(xs) {
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-sm;
      }
    }
    
    .skeleton-card {
      @include card-base;
      padding: $spacing-base;
      
      .skeleton {
        border-radius: $border-radius-base;
        margin-bottom: $spacing-sm;
        
        &.skeleton-image {
          height: 200px;
          
          @include respond-to(xs) {
            height: 150px;
          }
        }
        
        &.skeleton-title {
          height: 40px;
        }
        
        &.skeleton-price {
          height: 24px;
          width: 60%;
        }
        
        &.skeleton-button {
          height: 32px;
          margin-bottom: 0;
        }
      }
    }
  }
  
  // 空状态
  .empty-state {
    text-align: center;
    padding: $spacing-xxl 0;
    
    .empty-icon {
      font-size: 64px;
      margin-bottom: $spacing-lg;
      opacity: 0.5;
    }
    
    .empty-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: $text-primary;
      margin-bottom: $spacing-sm;
    }
    
    .empty-description {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin: 0;
    }
  }
}

// 网格项动画
.grid-container > * {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 交错动画效果
.grid-container > *:nth-child(1) { animation-delay: 0.1s; }
.grid-container > *:nth-child(2) { animation-delay: 0.2s; }
.grid-container > *:nth-child(3) { animation-delay: 0.3s; }
.grid-container > *:nth-child(4) { animation-delay: 0.4s; }
.grid-container > *:nth-child(5) { animation-delay: 0.5s; }
.grid-container > *:nth-child(6) { animation-delay: 0.6s; }
</style>
