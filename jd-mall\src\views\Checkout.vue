<template>
  <div class="checkout-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">确认订单</h1>
      </div>

      <div class="checkout-content">
        <!-- 收货地址 -->
        <div class="address-section">
          <h3 class="section-title">收货地址</h3>
          
          <div v-if="!selectedAddress" class="no-address">
            <p>您还没有收货地址，请先添加地址</p>
            <button class="btn btn-primary" @click="showAddressForm = true">
              添加新地址
            </button>
          </div>
          
          <div v-else class="selected-address">
            <div class="address-info">
              <div class="recipient">
                <span class="name">{{ selectedAddress.name }}</span>
                <span class="phone">{{ selectedAddress.phone }}</span>
              </div>
              <div class="address">
                {{ selectedAddress.province }} {{ selectedAddress.city }} {{ selectedAddress.district }} {{ selectedAddress.detail }}
              </div>
            </div>
            <button class="btn btn-secondary" @click="showAddressList = true">
              更换地址
            </button>
          </div>
        </div>

        <!-- 商品清单 -->
        <div class="products-section">
          <h3 class="section-title">商品清单</h3>
          
          <div class="products-list">
            <div 
              class="product-item"
              v-for="item in selectedItems"
              :key="`${item.id}-${JSON.stringify(item.selectedSpec)}`"
            >
              <img :src="item.image" :alt="item.title" class="product-image" />
              <div class="product-info">
                <h4 class="product-title">{{ item.title }}</h4>
                <div class="product-specs" v-if="item.selectedSpec">
                  <span 
                    class="spec-tag"
                    v-for="(value, key) in item.selectedSpec"
                    :key="key"
                  >
                    {{ key }}: {{ value }}
                  </span>
                </div>
              </div>
              <div class="product-price">¥{{ formatPrice(item.price) }}</div>
              <div class="product-quantity">×{{ item.quantity }}</div>
              <div class="product-total">¥{{ formatPrice(item.price * item.quantity) }}</div>
            </div>
          </div>
        </div>

        <!-- 支付方式 -->
        <div class="payment-section">
          <h3 class="section-title">支付方式</h3>
          
          <div class="payment-methods">
            <label 
              class="payment-method"
              v-for="method in paymentMethods"
              :key="method.id"
            >
              <input 
                type="radio" 
                name="payment"
                :value="method.id"
                v-model="selectedPayment"
              />
              <span class="method-icon">{{ method.icon }}</span>
              <span class="method-name">{{ method.name }}</span>
              <span class="method-desc">{{ method.description }}</span>
            </label>
          </div>
        </div>

        <!-- 订单备注 -->
        <div class="remark-section">
          <h3 class="section-title">订单备注</h3>
          <textarea 
            class="remark-input"
            v-model="orderRemark"
            placeholder="选填，请输入订单备注"
            maxlength="200"
          ></textarea>
        </div>

        <!-- 费用明细 -->
        <div class="summary-section">
          <h3 class="section-title">费用明细</h3>
          
          <div class="summary-details">
            <div class="summary-item">
              <span class="label">商品总价：</span>
              <span class="value">¥{{ formatPrice(totalPrice) }}</span>
            </div>
            <div class="summary-item">
              <span class="label">运费：</span>
              <span class="value">¥{{ formatPrice(shippingFee) }}</span>
            </div>
            <div class="summary-item discount" v-if="discountAmount > 0">
              <span class="label">优惠减免：</span>
              <span class="value">-¥{{ formatPrice(discountAmount) }}</span>
            </div>
            <div class="summary-item total">
              <span class="label">应付总额：</span>
              <span class="value">¥{{ formatPrice(finalAmount) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="checkout-footer">
        <div class="footer-left">
          <div class="item-count">共 {{ selectedCount }} 件商品</div>
          <div class="total-amount">
            应付总额：<span class="amount">¥{{ formatPrice(finalAmount) }}</span>
          </div>
        </div>
        
        <div class="footer-right">
          <button class="btn btn-secondary" @click="$router.back()">
            返回购物车
          </button>
          <button 
            class="btn btn-primary submit-btn"
            @click="submitOrder"
            :disabled="!canSubmit || isSubmitting"
          >
            {{ isSubmitting ? '提交中...' : '提交订单' }}
          </button>
        </div>
      </div>

      <!-- 地址列表弹窗 -->
      <div v-if="showAddressList" class="modal-overlay" @click="showAddressList = false">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>选择收货地址</h3>
            <button class="close-btn" @click="showAddressList = false">×</button>
          </div>
          
          <div class="modal-body">
            <div class="address-list">
              <div 
                class="address-item"
                v-for="address in userAddresses"
                :key="address.id"
                :class="{ active: selectedAddress?.id === address.id }"
                @click="selectAddress(address)"
              >
                <div class="address-info">
                  <div class="recipient">
                    <span class="name">{{ address.name }}</span>
                    <span class="phone">{{ address.phone }}</span>
                    <span class="default-tag" v-if="address.isDefault">默认</span>
                  </div>
                  <div class="address">
                    {{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}
                  </div>
                </div>
              </div>
            </div>
            
            <button class="btn btn-primary add-address-btn" @click="showAddressForm = true">
              添加新地址
            </button>
          </div>
        </div>
      </div>

      <!-- 添加地址表单弹窗 -->
      <div v-if="showAddressForm" class="modal-overlay" @click="showAddressForm = false">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>添加收货地址</h3>
            <button class="close-btn" @click="showAddressForm = false">×</button>
          </div>
          
          <div class="modal-body">
            <form class="address-form" @submit.prevent="saveAddress">
              <div class="form-row">
                <div class="form-group">
                  <label>收货人姓名</label>
                  <input type="text" v-model="newAddress.name" required />
                </div>
                <div class="form-group">
                  <label>手机号码</label>
                  <input type="tel" v-model="newAddress.phone" required />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>省份</label>
                  <select v-model="newAddress.province" required>
                    <option value="">请选择省份</option>
                    <option value="北京市">北京市</option>
                    <option value="上海市">上海市</option>
                    <option value="广东省">广东省</option>
                    <option value="浙江省">浙江省</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>城市</label>
                  <select v-model="newAddress.city" required>
                    <option value="">请选择城市</option>
                    <option value="北京市">北京市</option>
                    <option value="上海市">上海市</option>
                    <option value="广州市">广州市</option>
                    <option value="杭州市">杭州市</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>区县</label>
                  <select v-model="newAddress.district" required>
                    <option value="">请选择区县</option>
                    <option value="朝阳区">朝阳区</option>
                    <option value="海淀区">海淀区</option>
                    <option value="浦东新区">浦东新区</option>
                    <option value="天河区">天河区</option>
                  </select>
                </div>
              </div>
              
              <div class="form-group">
                <label>详细地址</label>
                <textarea v-model="newAddress.detail" placeholder="请输入详细地址" required></textarea>
              </div>
              
              <div class="form-actions">
                <button type="button" class="btn btn-secondary" @click="showAddressForm = false">
                  取消
                </button>
                <button type="submit" class="btn btn-primary">
                  保存地址
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart.js'
import { useUserStore } from '../stores/user.js'
import { useOrderStore } from '../stores/order.js'

export default {
  name: 'Checkout',
  setup() {
    const router = useRouter()
    const cartStore = useCartStore()
    const userStore = useUserStore()
    const orderStore = useOrderStore()

    // 响应式数据
    const selectedAddress = ref(null)
    const selectedPayment = ref('alipay')
    const orderRemark = ref('')
    const showAddressList = ref(false)
    const showAddressForm = ref(false)
    const isSubmitting = ref(false)
    const newAddress = ref({
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: ''
    })

    // 支付方式
    const paymentMethods = [
      { id: 'alipay', name: '支付宝', icon: '💰', description: '推荐使用' },
      { id: 'wechat', name: '微信支付', icon: '💚', description: '安全便捷' },
      { id: 'unionpay', name: '银联支付', icon: '💳', description: '银行卡支付' }
    ]

    // 计算属性
    const selectedItems = computed(() => cartStore.selectedItems)
    const selectedCount = computed(() => cartStore.selectedCount)
    const totalPrice = computed(() => cartStore.selectedTotalPrice)
    const userAddresses = computed(() => userStore.user?.addresses || [])
    
    const shippingFee = computed(() => {
      return totalPrice.value >= 99 ? 0 : 10
    })
    
    const discountAmount = computed(() => {
      return totalPrice.value >= 200 ? 20 : 0
    })
    
    const finalAmount = computed(() => {
      return totalPrice.value + shippingFee.value - discountAmount.value
    })

    const canSubmit = computed(() => {
      return selectedAddress.value && selectedPayment.value && selectedItems.value.length > 0
    })

    // 方法
    const selectAddress = (address) => {
      selectedAddress.value = address
      showAddressList.value = false
    }

    const saveAddress = async () => {
      const result = await userStore.addAddress(newAddress.value)
      if (result.success) {
        selectedAddress.value = result.address
        showAddressForm.value = false
        // 重置表单
        newAddress.value = {
          name: '',
          phone: '',
          province: '',
          city: '',
          district: '',
          detail: ''
        }
      }
    }

    const submitOrder = async () => {
      if (!canSubmit.value) return

      isSubmitting.value = true
      try {
        const orderData = {
          userId: userStore.user?.id,
          items: selectedItems.value,
          shippingAddress: selectedAddress.value,
          paymentMethod: selectedPayment.value,
          totalAmount: finalAmount.value,
          shippingFee: shippingFee.value,
          discountAmount: discountAmount.value,
          remark: orderRemark.value
        }

        const result = await orderStore.createOrder(orderData)
        if (result.success) {
          // 清除已选中的购物车商品
          cartStore.clearSelected()
          
          // 跳转到支付页面
          router.push(`/order/${result.data.id}`)
        } else {
          alert('订单创建失败：' + result.error)
        }
      } catch (error) {
        console.error('提交订单失败:', error)
        alert('提交订单失败，请重试')
      } finally {
        isSubmitting.value = false
      }
    }

    const formatPrice = (price) => {
      return Number(price).toFixed(2)
    }

    // 组件挂载时初始化
    onMounted(() => {
      // 检查是否有选中的商品
      if (selectedItems.value.length === 0) {
        router.push('/cart')
        return
      }

      // 设置默认地址
      const defaultAddress = userAddresses.value.find(addr => addr.isDefault)
      if (defaultAddress) {
        selectedAddress.value = defaultAddress
      }
    })

    return {
      selectedAddress,
      selectedPayment,
      orderRemark,
      showAddressList,
      showAddressForm,
      isSubmitting,
      newAddress,
      paymentMethods,
      selectedItems,
      selectedCount,
      totalPrice,
      userAddresses,
      shippingFee,
      discountAmount,
      finalAmount,
      canSubmit,
      selectAddress,
      saveAddress,
      submitOrder,
      formatPrice
    }
  }
}
</script>
