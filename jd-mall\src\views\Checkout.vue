<template>
  <div class="checkout-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">确认订单</h1>
      </div>

      <div class="checkout-content">
        <!-- 收货地址 -->
        <div class="address-section">
          <h3 class="section-title">收货地址</h3>
          
          <div v-if="!selectedAddress" class="no-address">
            <p>您还没有收货地址，请先添加地址</p>
            <button class="btn btn-primary" @click="showAddressForm = true">
              添加新地址
            </button>
          </div>
          
          <div v-else class="selected-address">
            <div class="address-info">
              <div class="recipient">
                <span class="name">{{ selectedAddress.name }}</span>
                <span class="phone">{{ selectedAddress.phone }}</span>
              </div>
              <div class="address">
                {{ selectedAddress.province }} {{ selectedAddress.city }} {{ selectedAddress.district }} {{ selectedAddress.detail }}
              </div>
            </div>
            <button class="btn btn-secondary" @click="showAddressList = true">
              更换地址
            </button>
          </div>
        </div>

        <!-- 商品清单 -->
        <div class="products-section">
          <h3 class="section-title">商品清单</h3>
          
          <div class="products-list">
            <div 
              class="product-item"
              v-for="item in selectedItems"
              :key="`${item.id}-${JSON.stringify(item.selectedSpec)}`"
            >
              <img :src="item.image" :alt="item.title" class="product-image" />
              <div class="product-info">
                <h4 class="product-title">{{ item.title }}</h4>
                <div class="product-specs" v-if="item.selectedSpec">
                  <span 
                    class="spec-tag"
                    v-for="(value, key) in item.selectedSpec"
                    :key="key"
                  >
                    {{ key }}: {{ value }}
                  </span>
                </div>
              </div>
              <div class="product-price">¥{{ formatPrice(item.price) }}</div>
              <div class="product-quantity">×{{ item.quantity }}</div>
              <div class="product-total">¥{{ formatPrice(item.price * item.quantity) }}</div>
            </div>
          </div>
        </div>

        <!-- 支付方式 -->
        <div class="payment-section">
          <h3 class="section-title">支付方式</h3>
          
          <div class="payment-methods">
            <label 
              class="payment-method"
              v-for="method in paymentMethods"
              :key="method.id"
            >
              <input 
                type="radio" 
                name="payment"
                :value="method.id"
                v-model="selectedPayment"
              />
              <span class="method-icon">{{ method.icon }}</span>
              <span class="method-name">{{ method.name }}</span>
              <span class="method-desc">{{ method.description }}</span>
            </label>
          </div>
        </div>

        <!-- 订单备注 -->
        <div class="remark-section">
          <h3 class="section-title">订单备注</h3>
          <textarea 
            class="remark-input"
            v-model="orderRemark"
            placeholder="选填，请输入订单备注"
            maxlength="200"
          ></textarea>
        </div>

        <!-- 费用明细 -->
        <div class="summary-section">
          <h3 class="section-title">费用明细</h3>
          
          <div class="summary-details">
            <div class="summary-item">
              <span class="label">商品总价：</span>
              <span class="value">¥{{ formatPrice(totalPrice) }}</span>
            </div>
            <div class="summary-item">
              <span class="label">运费：</span>
              <span class="value">¥{{ formatPrice(shippingFee) }}</span>
            </div>
            <div class="summary-item discount" v-if="discountAmount > 0">
              <span class="label">优惠减免：</span>
              <span class="value">-¥{{ formatPrice(discountAmount) }}</span>
            </div>
            <div class="summary-item total">
              <span class="label">应付总额：</span>
              <span class="value">¥{{ formatPrice(finalAmount) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="checkout-footer">
        <div class="footer-left">
          <div class="item-count">共 {{ selectedCount }} 件商品</div>
          <div class="total-amount">
            应付总额：<span class="amount">¥{{ formatPrice(finalAmount) }}</span>
          </div>
        </div>
        
        <div class="footer-right">
          <button class="btn btn-secondary" @click="$router.back()">
            返回购物车
          </button>
          <button 
            class="btn btn-primary submit-btn"
            @click="submitOrder"
            :disabled="!canSubmit || isSubmitting"
          >
            {{ isSubmitting ? '提交中...' : '提交订单' }}
          </button>
        </div>
      </div>

      <!-- 地址列表弹窗 -->
      <div v-if="showAddressList" class="modal-overlay" @click="showAddressList = false">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>选择收货地址</h3>
            <button class="close-btn" @click="showAddressList = false">×</button>
          </div>
          
          <div class="modal-body">
            <div class="address-list">
              <div 
                class="address-item"
                v-for="address in userAddresses"
                :key="address.id"
                :class="{ active: selectedAddress?.id === address.id }"
                @click="selectAddress(address)"
              >
                <div class="address-info">
                  <div class="recipient">
                    <span class="name">{{ address.name }}</span>
                    <span class="phone">{{ address.phone }}</span>
                    <span class="default-tag" v-if="address.isDefault">默认</span>
                  </div>
                  <div class="address">
                    {{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}
                  </div>
                </div>
              </div>
            </div>
            
            <button class="btn btn-primary add-address-btn" @click="showAddressForm = true">
              添加新地址
            </button>
          </div>
        </div>
      </div>

      <!-- 添加地址表单弹窗 -->
      <div v-if="showAddressForm" class="modal-overlay" @click="showAddressForm = false">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>添加收货地址</h3>
            <button class="close-btn" @click="showAddressForm = false">×</button>
          </div>
          
          <div class="modal-body">
            <form class="address-form" @submit.prevent="saveAddress">
              <div class="form-row">
                <div class="form-group">
                  <label>收货人姓名</label>
                  <input type="text" v-model="newAddress.name" required />
                </div>
                <div class="form-group">
                  <label>手机号码</label>
                  <input type="tel" v-model="newAddress.phone" required />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>省份</label>
                  <select v-model="newAddress.province" required>
                    <option value="">请选择省份</option>
                    <option value="北京市">北京市</option>
                    <option value="上海市">上海市</option>
                    <option value="广东省">广东省</option>
                    <option value="浙江省">浙江省</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>城市</label>
                  <select v-model="newAddress.city" required>
                    <option value="">请选择城市</option>
                    <option value="北京市">北京市</option>
                    <option value="上海市">上海市</option>
                    <option value="广州市">广州市</option>
                    <option value="杭州市">杭州市</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>区县</label>
                  <select v-model="newAddress.district" required>
                    <option value="">请选择区县</option>
                    <option value="朝阳区">朝阳区</option>
                    <option value="海淀区">海淀区</option>
                    <option value="浦东新区">浦东新区</option>
                    <option value="天河区">天河区</option>
                  </select>
                </div>
              </div>
              
              <div class="form-group">
                <label>详细地址</label>
                <textarea v-model="newAddress.detail" placeholder="请输入详细地址" required></textarea>
              </div>
              
              <div class="form-actions">
                <button type="button" class="btn btn-secondary" @click="showAddressForm = false">
                  取消
                </button>
                <button type="submit" class="btn btn-primary">
                  保存地址
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart.js'
import { useUserStore } from '../stores/user.js'
import { useOrderStore } from '../stores/order.js'

export default {
  name: 'Checkout',
  setup() {
    const router = useRouter()
    const cartStore = useCartStore()
    const userStore = useUserStore()
    const orderStore = useOrderStore()

    // 响应式数据
    const selectedAddress = ref(null)
    const selectedPayment = ref('alipay')
    const orderRemark = ref('')
    const showAddressList = ref(false)
    const showAddressForm = ref(false)
    const isSubmitting = ref(false)
    const newAddress = ref({
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: ''
    })

    // 支付方式
    const paymentMethods = [
      { id: 'alipay', name: '支付宝', icon: '💰', description: '推荐使用' },
      { id: 'wechat', name: '微信支付', icon: '💚', description: '安全便捷' },
      { id: 'unionpay', name: '银联支付', icon: '💳', description: '银行卡支付' }
    ]

    // 计算属性
    const selectedItems = computed(() => cartStore.selectedItems)
    const selectedCount = computed(() => cartStore.selectedCount)
    const totalPrice = computed(() => cartStore.selectedTotalPrice)
    const userAddresses = computed(() => userStore.user?.addresses || [])
    
    const shippingFee = computed(() => {
      return totalPrice.value >= 99 ? 0 : 10
    })
    
    const discountAmount = computed(() => {
      return totalPrice.value >= 200 ? 20 : 0
    })
    
    const finalAmount = computed(() => {
      return totalPrice.value + shippingFee.value - discountAmount.value
    })

    const canSubmit = computed(() => {
      return selectedAddress.value && selectedPayment.value && selectedItems.value.length > 0
    })

    // 方法
    const selectAddress = (address) => {
      selectedAddress.value = address
      showAddressList.value = false
    }

    const saveAddress = async () => {
      const result = await userStore.addAddress(newAddress.value)
      if (result.success) {
        selectedAddress.value = result.address
        showAddressForm.value = false
        // 重置表单
        newAddress.value = {
          name: '',
          phone: '',
          province: '',
          city: '',
          district: '',
          detail: ''
        }
      }
    }

    const submitOrder = async () => {
      if (!canSubmit.value) return

      isSubmitting.value = true
      try {
        const orderData = {
          userId: userStore.user?.id,
          items: selectedItems.value,
          shippingAddress: selectedAddress.value,
          paymentMethod: selectedPayment.value,
          totalAmount: finalAmount.value,
          shippingFee: shippingFee.value,
          discountAmount: discountAmount.value,
          remark: orderRemark.value
        }

        const result = await orderStore.createOrder(orderData)
        if (result.success) {
          // 清除已选中的购物车商品
          cartStore.clearSelected()
          
          // 跳转到支付页面
          router.push(`/order/${result.data.id}`)
        } else {
          alert('订单创建失败：' + result.error)
        }
      } catch (error) {
        console.error('提交订单失败:', error)
        alert('提交订单失败，请重试')
      } finally {
        isSubmitting.value = false
      }
    }

    const formatPrice = (price) => {
      return Number(price).toFixed(2)
    }

    // 组件挂载时初始化
    onMounted(() => {
      // 检查是否有选中的商品
      if (selectedItems.value.length === 0) {
        router.push('/cart')
        return
      }

      // 设置默认地址
      const defaultAddress = userAddresses.value.find(addr => addr.isDefault)
      if (defaultAddress) {
        selectedAddress.value = defaultAddress
      }
    })

    return {
      selectedAddress,
      selectedPayment,
      orderRemark,
      showAddressList,
      showAddressForm,
      isSubmitting,
      newAddress,
      paymentMethods,
      selectedItems,
      selectedCount,
      totalPrice,
      userAddresses,
      shippingFee,
      discountAmount,
      finalAmount,
      canSubmit,
      selectAddress,
      saveAddress,
      submitOrder,
      formatPrice
    }
  }
}
</script>

<style lang="scss" scoped>
.checkout-page {
  min-height: 100vh;
  background-color: $bg-secondary;
  padding: $spacing-6 0;

  .page-header {
    margin-bottom: $spacing-6;

    .page-title {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
    }
  }

  .checkout-content {
    > div {
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-6;
      margin-bottom: $spacing-4;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .section-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-4;
      padding-bottom: $spacing-2;
      border-bottom: 1px solid $border-light;
    }

    // 收货地址
    .address-section {
      .no-address {
        text-align: center;
        padding: $spacing-8 0;

        p {
          color: $text-secondary;
          margin-bottom: $spacing-4;
        }
      }

      .selected-address {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: $spacing-4;
        border: 1px solid $border-medium;
        border-radius: $border-radius-base;

        @include respond-to(sm) {
          flex-direction: column;
          gap: $spacing-3;
        }

        .address-info {
          flex: 1;

          .recipient {
            display: flex;
            gap: $spacing-4;
            margin-bottom: $spacing-2;

            .name {
              font-weight: $font-weight-semibold;
              color: $text-primary;
            }

            .phone {
              color: $text-secondary;
            }
          }

          .address {
            color: $text-secondary;
            line-height: $line-height-relaxed;
          }
        }
      }
    }

    // 商品清单
    .products-section {
      .products-list {
        .product-item {
          display: flex;
          align-items: center;
          gap: $spacing-4;
          padding: $spacing-4 0;
          border-bottom: 1px solid $border-light;

          &:last-child {
            border-bottom: none;
          }

          @include respond-to(sm) {
            flex-wrap: wrap;
            gap: $spacing-2;
          }

          .product-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: $border-radius-base;
            flex-shrink: 0;
          }

          .product-info {
            flex: 1;
            min-width: 0;

            .product-title {
              font-size: $font-size-base;
              font-weight: $font-weight-semibold;
              color: $text-primary;
              margin-bottom: $spacing-2;
              @include text-ellipsis;
            }

            .product-specs {
              display: flex;
              flex-wrap: wrap;
              gap: $spacing-1;

              .spec-tag {
                background-color: $bg-tertiary;
                color: $text-secondary;
                padding: $spacing-1 $spacing-2;
                border-radius: $border-radius-sm;
                font-size: $font-size-sm;
              }
            }
          }

          .product-price {
            font-weight: $font-weight-semibold;
            color: $primary-color;
            min-width: 80px;
            text-align: right;
          }

          .product-quantity {
            color: $text-secondary;
            min-width: 40px;
            text-align: center;
          }

          .product-total {
            font-weight: $font-weight-bold;
            color: $primary-color;
            min-width: 100px;
            text-align: right;
          }
        }
      }
    }

    // 支付方式
    .payment-section {
      .payment-methods {
        display: flex;
        flex-direction: column;
        gap: $spacing-3;

        .payment-method {
          display: flex;
          align-items: center;
          gap: $spacing-3;
          padding: $spacing-4;
          border: 1px solid $border-medium;
          border-radius: $border-radius-base;
          cursor: pointer;
          transition: all $transition-fast $ease-out;

          &:hover {
            border-color: $primary-color;
            background-color: $primary-light;
          }

          input[type="radio"] {
            margin: 0;
          }

          .method-icon {
            font-size: $font-size-lg;
          }

          .method-name {
            font-weight: $font-weight-semibold;
            color: $text-primary;
            margin-right: $spacing-2;
          }

          .method-desc {
            color: $text-secondary;
            font-size: $font-size-sm;
          }
        }
      }
    }

    // 订单备注
    .remark-section {
      .remark-input {
        width: 100%;
        min-height: 80px;
        padding: $spacing-3;
        border: 1px solid $border-medium;
        border-radius: $border-radius-base;
        font-size: $font-size-base;
        font-family: inherit;
        resize: vertical;

        &:focus {
          outline: none;
          border-color: $primary-color;
          box-shadow: $box-shadow-focus;
        }

        &::placeholder {
          color: $text-placeholder;
        }
      }
    }

    // 费用明细
    .summary-section {
      .summary-details {
        .summary-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: $spacing-3 0;
          border-bottom: 1px solid $border-light;

          &:last-child {
            border-bottom: none;
          }

          .label {
            color: $text-secondary;
          }

          .value {
            font-weight: $font-weight-semibold;
            color: $text-primary;

            &.discount {
              color: $success-color;
            }
          }

          &.total {
            padding-top: $spacing-4;
            border-top: 2px solid $border-medium;
            font-size: $font-size-lg;

            .label {
              color: $text-primary;
              font-weight: $font-weight-semibold;
            }

            .value {
              color: $primary-color;
              font-size: $font-size-xl;
              font-weight: $font-weight-bold;
            }
          }
        }
      }
    }
  }

  // 底部操作栏
  .checkout-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $bg-primary;
    border-top: 1px solid $border-light;
    padding: $spacing-4 0;
    box-shadow: $box-shadow-lg;
    z-index: $z-index-sticky;

    > div {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 $spacing-6;
      display: flex;
      justify-content: space-between;
      align-items: center;

      @include respond-to(sm) {
        flex-direction: column;
        gap: $spacing-3;
        align-items: stretch;
      }
    }

    .footer-left {
      display: flex;
      flex-direction: column;
      gap: $spacing-1;

      @include respond-to(sm) {
        align-items: center;
      }

      .item-count {
        color: $text-secondary;
        font-size: $font-size-sm;
      }

      .total-amount {
        font-size: $font-size-base;
        color: $text-primary;

        .amount {
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          color: $primary-color;
          margin-left: $spacing-2;
        }
      }
    }

    .footer-right {
      display: flex;
      gap: $spacing-3;

      @include respond-to(sm) {
        justify-content: center;
      }

      .submit-btn {
        padding: $spacing-3 $spacing-8;
        font-size: $font-size-base;
        font-weight: $font-weight-semibold;
        border-radius: $border-radius-lg;
        min-width: 120px;
      }
    }
  }

  // 弹窗样式
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: $z-index-modal;
    padding: $spacing-4;

    .modal-content {
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      max-width: 600px;
      width: 100%;
      max-height: 80vh;
      overflow-y: auto;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: $spacing-4 $spacing-6;
        border-bottom: 1px solid $border-light;

        h3 {
          font-size: $font-size-lg;
          font-weight: $font-weight-bold;
          color: $text-primary;
          margin: 0;
        }

        .close-btn {
          background: none;
          border: none;
          font-size: $font-size-xxl;
          color: $text-tertiary;
          cursor: pointer;
          padding: 0;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: $text-primary;
          }
        }
      }

      .modal-body {
        padding: $spacing-6;

        // 地址列表
        .address-list {
          margin-bottom: $spacing-6;

          .address-item {
            padding: $spacing-4;
            border: 1px solid $border-medium;
            border-radius: $border-radius-base;
            margin-bottom: $spacing-3;
            cursor: pointer;
            transition: all $transition-fast $ease-out;

            &:hover {
              border-color: $primary-color;
              background-color: $primary-light;
            }

            &.active {
              border-color: $primary-color;
              background-color: $primary-light;
            }

            &:last-child {
              margin-bottom: 0;
            }

            .address-info {
              .recipient {
                display: flex;
                gap: $spacing-4;
                margin-bottom: $spacing-2;

                .name {
                  font-weight: $font-weight-semibold;
                  color: $text-primary;
                }

                .phone {
                  color: $text-secondary;
                }

                .default-tag {
                  background-color: $primary-color;
                  color: $text-white;
                  padding: $spacing-1 $spacing-2;
                  border-radius: $border-radius-sm;
                  font-size: $font-size-xs;
                }
              }

              .address {
                color: $text-secondary;
                line-height: $line-height-relaxed;
              }
            }
          }
        }

        .add-address-btn {
          width: 100%;
          padding: $spacing-3 0;
          border: 1px dashed $border-medium;
          background-color: $bg-tertiary;
          color: $text-secondary;
          border-radius: $border-radius-base;
          cursor: pointer;
          transition: all $transition-fast $ease-out;

          &:hover {
            border-color: $primary-color;
            color: $primary-color;
            background-color: $primary-light;
          }
        }

        // 地址表单
        .address-form {
          .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: $spacing-4;
            margin-bottom: $spacing-4;

            @include respond-to(sm) {
              grid-template-columns: 1fr;
            }
          }

          .form-group {
            margin-bottom: $spacing-4;

            label {
              display: block;
              font-weight: $font-weight-semibold;
              color: $text-primary;
              margin-bottom: $spacing-2;
            }

            input,
            select,
            textarea {
              width: 100%;
              padding: $spacing-3;
              border: 1px solid $border-medium;
              border-radius: $border-radius-base;
              font-size: $font-size-base;

              &:focus {
                outline: none;
                border-color: $primary-color;
                box-shadow: $box-shadow-focus;
              }

              &::placeholder {
                color: $text-placeholder;
              }
            }

            textarea {
              min-height: 80px;
              resize: vertical;
              font-family: inherit;
            }
          }

          .form-actions {
            display: flex;
            gap: $spacing-3;
            justify-content: flex-end;
            margin-top: $spacing-6;

            @include respond-to(sm) {
              flex-direction: column;
            }
          }
        }
      }
    }
  }
}</style>
