import './assets/scss/main.scss'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { performanceMonitor } from './utils/performance.js'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 使用插件
app.use(pinia)
app.use(router)

// 初始化性能监控
if (process.env.NODE_ENV === 'production') {
  performanceMonitor.init()
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
}

// 全局属性
app.config.globalProperties.$message = {
  success: (msg) => console.log('✅', msg),
  error: (msg) => console.error('❌', msg),
  warning: (msg) => console.warn('⚠️', msg),
  info: (msg) => console.info('ℹ️', msg)
}

// 初始化用户数据和购物车
import { useUserStore } from './stores/user.js'
import { useCartStore } from './stores/cart.js'

const userStore = useUserStore()
const cartStore = useCartStore()

// 初始化用户状态
userStore.initUser()

// 加载购物车数据
cartStore.loadFromStorage()

app.mount('#app')
