<template>
  <div class="orders-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">我的订单</h1>
      </div>

      <div class="orders-content">
        <!-- 订单状态筛选 -->
        <div class="order-tabs">
          <button 
            class="tab-btn"
            v-for="(tab, key) in orderTabs"
            :key="key"
            :class="{ active: activeTab === key }"
            @click="activeTab = key"
          >
            {{ tab.label }}
            <span class="count" v-if="tab.count > 0">({{ tab.count }})</span>
          </button>
        </div>

        <!-- 订单列表 -->
        <div class="orders-list">
          <div v-if="currentOrders.length === 0" class="empty-orders">
            <div class="empty-icon">📋</div>
            <h3>暂无订单</h3>
            <p>您还没有相关订单</p>
            <button class="btn btn-primary" @click="$router.push('/')">
              去购物
            </button>
          </div>

          <div 
            class="order-item"
            v-for="order in currentOrders"
            :key="order.id"
          >
            <div class="order-header">
              <div class="order-info">
                <span class="order-id">订单号：{{ order.id }}</span>
                <span class="order-time">{{ formatDate(order.createTime) }}</span>
              </div>
              <div class="order-status">{{ order.statusText }}</div>
            </div>

            <div class="order-products">
              <div 
                class="product-item"
                v-for="item in order.items"
                :key="item.id"
              >
                <img :src="item.image" :alt="item.title" class="product-image" />
                <div class="product-info">
                  <h4 class="product-title">{{ item.title }}</h4>
                  <div class="product-specs" v-if="item.selectedSpec">
                    <span 
                      class="spec-tag"
                      v-for="(value, key) in item.selectedSpec"
                      :key="key"
                    >
                      {{ key }}: {{ value }}
                    </span>
                  </div>
                </div>
                <div class="product-price">¥{{ formatPrice(item.price) }}</div>
                <div class="product-quantity">×{{ item.quantity }}</div>
              </div>
            </div>

            <div class="order-footer">
              <div class="order-total">
                共{{ order.items.length }}件商品，合计：
                <span class="amount">¥{{ formatPrice(order.totalAmount) }}</span>
              </div>
              <div class="order-actions">
                <button 
                  class="btn btn-secondary"
                  @click="viewOrder(order.id)"
                >
                  查看详情
                </button>
                <button 
                  class="btn btn-primary"
                  v-if="order.status === 'pending'"
                  @click="payOrder(order.id)"
                >
                  立即付款
                </button>
                <button 
                  class="btn btn-secondary"
                  v-if="order.status === 'delivered'"
                  @click="confirmOrder(order.id)"
                >
                  确认收货
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderStore } from '../stores/order.js'

export default {
  name: 'Orders',
  setup() {
    const router = useRouter()
    const orderStore = useOrderStore()

    const activeTab = ref('all')

    const orderTabs = computed(() => {
      const orders = orderStore.ordersByStatus
      return {
        all: { label: '全部订单', count: orders.all.length },
        pending: { label: '待付款', count: orders.pending.length },
        paid: { label: '待发货', count: orders.paid.length },
        shipped: { label: '待收货', count: orders.shipped.length },
        completed: { label: '已完成', count: orders.completed.length }
      }
    })

    const currentOrders = computed(() => {
      return orderStore.ordersByStatus[activeTab.value] || []
    })

    const viewOrder = (orderId) => {
      router.push(`/order/${orderId}`)
    }

    const payOrder = async (orderId) => {
      // 跳转到支付页面
      router.push(`/order/${orderId}`)
    }

    const confirmOrder = async (orderId) => {
      if (confirm('确认收货吗？')) {
        await orderStore.completeOrder(orderId)
      }
    }

    const formatPrice = (price) => {
      return Number(price).toFixed(2)
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString('zh-CN')
    }

    onMounted(() => {
      orderStore.fetchOrders()
    })

    return {
      activeTab,
      orderTabs,
      currentOrders,
      viewOrder,
      payOrder,
      confirmOrder,
      formatPrice,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
.orders-page {
  min-height: 100vh;
  background-color: $bg-secondary;
  padding: $spacing-6 0;
  
  .page-header {
    margin-bottom: $spacing-6;
    
    .page-title {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
    }
  }
  
  .orders-content {
    .order-tabs {
      display: flex;
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-2;
      margin-bottom: $spacing-6;
      overflow-x: auto;
      
      .tab-btn {
        flex: 1;
        padding: $spacing-3 $spacing-4;
        border: none;
        background: none;
        color: $text-secondary;
        cursor: pointer;
        border-radius: $border-radius-base;
        transition: all $transition-fast $ease-out;
        white-space: nowrap;
        
        &:hover {
          background-color: $bg-hover;
          color: $primary-color;
        }
        
        &.active {
          background-color: $primary-color;
          color: $text-white;
        }
        
        .count {
          margin-left: $spacing-1;
          font-size: $font-size-sm;
        }
      }
    }
    
    .orders-list {
      .empty-orders {
        text-align: center;
        padding: $spacing-16 $spacing-6;
        background-color: $bg-primary;
        border-radius: $border-radius-lg;
        
        .empty-icon {
          font-size: 80px;
          margin-bottom: $spacing-6;
          opacity: 0.5;
        }
        
        h3 {
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          color: $text-primary;
          margin-bottom: $spacing-3;
        }
        
        p {
          color: $text-secondary;
          margin-bottom: $spacing-6;
        }
      }
      
      .order-item {
        background-color: $bg-primary;
        border-radius: $border-radius-lg;
        margin-bottom: $spacing-4;
        overflow: hidden;
        
        .order-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: $spacing-4 $spacing-6;
          background-color: $bg-tertiary;
          border-bottom: 1px solid $border-light;
          
          .order-info {
            display: flex;
            gap: $spacing-4;
            
            .order-id {
              font-weight: $font-weight-semibold;
              color: $text-primary;
            }
            
            .order-time {
              color: $text-secondary;
              font-size: $font-size-sm;
            }
          }
          
          .order-status {
            color: $primary-color;
            font-weight: $font-weight-semibold;
          }
        }
        
        .order-products {
          padding: $spacing-4 $spacing-6;
          
          .product-item {
            display: flex;
            align-items: center;
            gap: $spacing-4;
            padding: $spacing-3 0;
            border-bottom: 1px solid $border-light;
            
            &:last-child {
              border-bottom: none;
            }
            
            .product-image {
              width: 60px;
              height: 60px;
              object-fit: cover;
              border-radius: $border-radius-base;
            }
            
            .product-info {
              flex: 1;
              
              .product-title {
                font-size: $font-size-base;
                font-weight: $font-weight-semibold;
                color: $text-primary;
                margin-bottom: $spacing-1;
                @include text-ellipsis;
              }
              
              .product-specs {
                display: flex;
                gap: $spacing-1;
                
                .spec-tag {
                  background-color: $bg-tertiary;
                  color: $text-secondary;
                  padding: $spacing-1 $spacing-2;
                  border-radius: $border-radius-sm;
                  font-size: $font-size-sm;
                }
              }
            }
            
            .product-price {
              font-weight: $font-weight-semibold;
              color: $primary-color;
            }
            
            .product-quantity {
              color: $text-secondary;
              min-width: 40px;
              text-align: right;
            }
          }
        }
        
        .order-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: $spacing-4 $spacing-6;
          background-color: $bg-tertiary;
          
          @include respond-to(sm) {
            flex-direction: column;
            gap: $spacing-3;
            align-items: stretch;
          }
          
          .order-total {
            font-size: $font-size-base;
            color: $text-primary;
            
            .amount {
              font-size: $font-size-lg;
              font-weight: $font-weight-bold;
              color: $primary-color;
              margin-left: $spacing-2;
            }
          }
          
          .order-actions {
            display: flex;
            gap: $spacing-3;
            
            @include respond-to(sm) {
              justify-content: center;
            }
          }
        }
      }
    }
  }
}
</style>
