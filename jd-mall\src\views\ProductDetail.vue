<template>
  <div class="product-detail-page">
    <div class="container">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <!-- 商品详情内容 -->
      <div v-else-if="product" class="product-detail">
        <!-- 商品主要信息 -->
        <div class="product-main">
          <!-- 商品图片区域 -->
          <div class="product-images">
            <div class="main-image">
              <img 
                :src="currentImage" 
                :alt="product.title"
                class="main-img"
                @click="showImageViewer = true"
              />
              <div class="zoom-hint">点击查看大图</div>
            </div>
            
            <div class="thumbnail-list">
              <div 
                class="thumbnail-item"
                v-for="(image, index) in product.images"
                :key="index"
                :class="{ active: currentImageIndex === index }"
                @click="selectImage(index)"
              >
                <img :src="image" :alt="`商品图片${index + 1}`" />
              </div>
            </div>
          </div>

          <!-- 商品信息区域 -->
          <div class="product-info">
            <h1 class="product-title">{{ product.title }}</h1>
            
            <div class="product-subtitle">{{ product.description }}</div>
            
            <!-- 价格信息 -->
            <div class="price-section">
              <div class="current-price">
                <span class="currency">¥</span>
                <span class="price">{{ formatPrice(product.price) }}</span>
              </div>
              <div class="original-price" v-if="product.originalPrice">
                <span>原价：¥{{ formatPrice(product.originalPrice) }}</span>
              </div>
              <div class="discount" v-if="product.originalPrice">
                <span class="discount-tag">
                  {{ Math.round((1 - product.price / product.originalPrice) * 100) }}折
                </span>
              </div>
            </div>

            <!-- 商品评价 -->
            <div class="rating-section">
              <div class="rating-stars">
                <span 
                  class="star" 
                  v-for="i in 5" 
                  :key="i"
                  :class="{ active: i <= Math.floor(product.rating) }"
                >
                  ★
                </span>
                <span class="rating-score">{{ product.rating }}</span>
              </div>
              <div class="rating-count">{{ product.reviewCount }}条评价</div>
            </div>

            <!-- 商品规格选择 -->
            <div class="specs-section" v-if="product.specs">
              <div 
                class="spec-group" 
                v-for="spec in product.specs" 
                :key="spec.name"
              >
                <div class="spec-label">{{ spec.name }}：</div>
                <div class="spec-options">
                  <button
                    class="spec-option"
                    v-for="option in spec.options"
                    :key="option"
                    :class="{ 
                      active: selectedSpecs[spec.name] === option,
                      disabled: !isSpecAvailable(spec.name, option)
                    }"
                    @click="selectSpec(spec.name, option)"
                    :disabled="!isSpecAvailable(spec.name, option)"
                  >
                    {{ option }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 数量选择 -->
            <div class="quantity-section">
              <div class="quantity-label">数量：</div>
              <div class="quantity-control">
                <button 
                  class="quantity-btn decrease"
                  @click="decreaseQuantity"
                  :disabled="quantity <= 1"
                >
                  -
                </button>
                <input 
                  type="number" 
                  class="quantity-input"
                  v-model.number="quantity"
                  :min="1"
                  :max="product.maxQuantity || 99"
                  @blur="validateQuantity"
                />
                <button 
                  class="quantity-btn increase"
                  @click="increaseQuantity"
                  :disabled="quantity >= (product.maxQuantity || 99)"
                >
                  +
                </button>
              </div>
              <div class="stock-info">
                库存：{{ product.stock || 999 }}件
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <button 
                class="btn btn-primary add-cart-btn"
                @click="addToCart"
                :disabled="!product.inStock || !canAddToCart"
              >
                <i class="icon-cart"></i>
                加入购物车
              </button>
              <button 
                class="btn btn-secondary buy-now-btn"
                @click="buyNow"
                :disabled="!product.inStock || !canAddToCart"
              >
                立即购买
              </button>
            </div>

            <!-- 服务保障 -->
            <div class="service-section">
              <div class="service-item">
                <i class="service-icon">🚚</i>
                <span>京东配送</span>
              </div>
              <div class="service-item">
                <i class="service-icon">🛡️</i>
                <span>正品保障</span>
              </div>
              <div class="service-item">
                <i class="service-icon">↩️</i>
                <span>7天无理由退货</span>
              </div>
              <div class="service-item">
                <i class="service-icon">⚡</i>
                <span>急速退款</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品详细信息 -->
        <div class="product-details">
          <div class="detail-tabs">
            <button 
              class="tab-btn"
              v-for="tab in detailTabs"
              :key="tab.key"
              :class="{ active: activeTab === tab.key }"
              @click="activeTab = tab.key"
            >
              {{ tab.label }}
            </button>
          </div>

          <div class="tab-content">
            <!-- 商品详情 -->
            <div v-if="activeTab === 'description'" class="description-content">
              <h3>商品描述</h3>
              <p>{{ product.description }}</p>
              
              <h3>商品规格</h3>
              <table class="spec-table">
                <tr v-for="spec in product.specifications" :key="spec.name">
                  <td class="spec-name">{{ spec.name }}</td>
                  <td class="spec-value">{{ spec.value }}</td>
                </tr>
              </table>
            </div>

            <!-- 商品评价 -->
            <div v-if="activeTab === 'reviews'" class="reviews-content">
              <div class="reviews-summary">
                <div class="rating-overview">
                  <div class="rating-score-large">{{ product.rating }}</div>
                  <div class="rating-stars-large">
                    <span 
                      class="star" 
                      v-for="i in 5" 
                      :key="i"
                      :class="{ active: i <= Math.floor(product.rating) }"
                    >
                      ★
                    </span>
                  </div>
                  <div class="rating-count">共{{ product.reviewCount }}条评价</div>
                </div>
              </div>

              <div class="reviews-list">
                <div 
                  class="review-item"
                  v-for="review in product.reviews?.slice(0, 10)"
                  :key="review.id"
                >
                  <div class="review-header">
                    <img :src="review.avatar" :alt="review.userName" class="user-avatar" />
                    <div class="user-info">
                      <div class="user-name">{{ review.userName }}</div>
                      <div class="review-rating">
                        <span 
                          class="star" 
                          v-for="i in 5" 
                          :key="i"
                          :class="{ active: i <= review.rating }"
                        >
                          ★
                        </span>
                      </div>
                    </div>
                    <div class="review-date">{{ formatDate(review.date) }}</div>
                  </div>
                  <div class="review-content">{{ review.content }}</div>
                  <div class="review-images" v-if="review.images?.length">
                    <img 
                      v-for="(image, index) in review.images"
                      :key="index"
                      :src="image"
                      :alt="`评价图片${index + 1}`"
                      class="review-image"
                    />
                  </div>
                  <div class="review-helpful">
                    <button class="helpful-btn">
                      👍 有用 ({{ review.helpful }})
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关商品推荐 -->
        <div class="related-products" v-if="product.relatedProducts?.length">
          <h3 class="section-title">相关商品推荐</h3>
          <div class="related-grid">
            <ProductCard
              v-for="relatedProduct in product.relatedProducts"
              :key="relatedProduct.id"
              :product="relatedProduct"
              @click="goToProduct"
              @add-to-cart="handleAddToCart"
            />
          </div>
        </div>
      </div>

      <!-- 商品不存在 -->
      <div v-else class="product-not-found">
        <div class="not-found-icon">📦</div>
        <h3>商品不存在</h3>
        <p>抱歉，您访问的商品不存在或已下架</p>
        <button class="btn btn-primary" @click="$router.push('/')">
          返回首页
        </button>
      </div>
    </div>

    <!-- 图片查看器 -->
    <div v-if="showImageViewer" class="image-viewer" @click="showImageViewer = false">
      <div class="viewer-content" @click.stop>
        <img :src="currentImage" :alt="product?.title" class="viewer-image" />
        <button class="close-btn" @click="showImageViewer = false">×</button>
        <button 
          class="nav-btn prev-btn" 
          @click="prevImage"
          v-if="product?.images?.length > 1"
        >
          ‹
        </button>
        <button 
          class="nav-btn next-btn" 
          @click="nextImage"
          v-if="product?.images?.length > 1"
        >
          ›
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductStore } from '../stores/product.js'
import { useCartStore } from '../stores/cart.js'
import ProductCard from '../components/common/ProductCard.vue'

export default {
  name: 'ProductDetail',
  components: {
    ProductCard
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const productStore = useProductStore()
    const cartStore = useCartStore()

    // 响应式数据
    const product = ref(null)
    const isLoading = ref(true)
    const currentImageIndex = ref(0)
    const selectedSpecs = ref({})
    const quantity = ref(1)
    const activeTab = ref('description')
    const showImageViewer = ref(false)

    // 详情标签
    const detailTabs = [
      { key: 'description', label: '商品详情' },
      { key: 'reviews', label: '商品评价' }
    ]

    // 计算属性
    const currentImage = computed(() => {
      return product.value?.images?.[currentImageIndex.value] || ''
    })

    const canAddToCart = computed(() => {
      if (!product.value?.specs) return true
      
      // 检查是否所有规格都已选择
      return product.value.specs.every(spec => selectedSpecs.value[spec.name])
    })

    // 方法
    const fetchProductDetail = async () => {
      isLoading.value = true
      try {
        const result = await productStore.fetchProductDetail(route.params.id)
        if (result.success) {
          product.value = result.data
          // 初始化选中的规格
          if (product.value.specs) {
            product.value.specs.forEach(spec => {
              selectedSpecs.value[spec.name] = spec.options[0]
            })
          }
        } else {
          console.error('获取商品详情失败:', result.error)
        }
      } catch (error) {
        console.error('获取商品详情失败:', error)
      } finally {
        isLoading.value = false
      }
    }

    const selectImage = (index) => {
      currentImageIndex.value = index
    }

    const selectSpec = (specName, option) => {
      selectedSpecs.value[specName] = option
    }

    const isSpecAvailable = (specName, option) => {
      // 这里可以实现规格可用性检查逻辑
      return true
    }

    const increaseQuantity = () => {
      if (quantity.value < (product.value?.maxQuantity || 99)) {
        quantity.value++
      }
    }

    const decreaseQuantity = () => {
      if (quantity.value > 1) {
        quantity.value--
      }
    }

    const validateQuantity = () => {
      if (quantity.value < 1) {
        quantity.value = 1
      } else if (quantity.value > (product.value?.maxQuantity || 99)) {
        quantity.value = product.value?.maxQuantity || 99
      }
    }

    const addToCart = () => {
      const result = cartStore.addToCart(
        product.value, 
        quantity.value, 
        selectedSpecs.value
      )
      
      if (result.success) {
        // 显示成功消息
        console.log('✅ 已添加到购物车')
      }
    }

    const buyNow = () => {
      addToCart()
      router.push('/cart')
    }

    const goToProduct = (relatedProduct) => {
      router.push(`/product/${relatedProduct.id}`)
    }

    const handleAddToCart = (relatedProduct) => {
      cartStore.addToCart(relatedProduct, 1)
    }

    const prevImage = () => {
      if (currentImageIndex.value > 0) {
        currentImageIndex.value--
      } else {
        currentImageIndex.value = product.value.images.length - 1
      }
    }

    const nextImage = () => {
      if (currentImageIndex.value < product.value.images.length - 1) {
        currentImageIndex.value++
      } else {
        currentImageIndex.value = 0
      }
    }

    const formatPrice = (price) => {
      return Number(price).toFixed(2)
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 监听路由变化
    watch(() => route.params.id, () => {
      if (route.params.id) {
        fetchProductDetail()
      }
    })

    // 组件挂载时获取商品详情
    onMounted(() => {
      fetchProductDetail()
    })

    return {
      product,
      isLoading,
      currentImageIndex,
      selectedSpecs,
      quantity,
      activeTab,
      showImageViewer,
      detailTabs,
      currentImage,
      canAddToCart,
      selectImage,
      selectSpec,
      isSpecAvailable,
      increaseQuantity,
      decreaseQuantity,
      validateQuantity,
      addToCart,
      buyNow,
      goToProduct,
      handleAddToCart,
      prevImage,
      nextImage,
      formatPrice,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
.product-detail-page {
  min-height: 100vh;
  background-color: $bg-secondary;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid $border-light;
      border-top: 3px solid $primary-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: $spacing-4;
    }
  }

  .product-detail {
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    overflow: hidden;
    margin-bottom: $spacing-6;

    .product-main {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: $spacing-8;
      padding: $spacing-8;

      @include respond-to(md) {
        grid-template-columns: 1fr;
        gap: $spacing-6;
        padding: $spacing-6;
      }
    }

    // 商品图片区域
    .product-images {
      .main-image {
        position: relative;
        margin-bottom: $spacing-4;
        border-radius: $border-radius-lg;
        overflow: hidden;
        background-color: $bg-tertiary;

        .main-img {
          width: 100%;
          height: 500px;
          object-fit: cover;
          cursor: zoom-in;
          transition: transform $transition-base $ease-out;

          &:hover {
            transform: scale(1.05);
          }

          @include respond-to(md) {
            height: 300px;
          }
        }

        .zoom-hint {
          position: absolute;
          bottom: $spacing-4;
          right: $spacing-4;
          background-color: rgba(0, 0, 0, 0.7);
          color: $text-white;
          padding: $spacing-1 $spacing-2;
          border-radius: $border-radius-base;
          font-size: $font-size-sm;
          opacity: 0;
          transition: opacity $transition-base $ease-out;
        }

        &:hover .zoom-hint {
          opacity: 1;
        }
      }

      .thumbnail-list {
        display: flex;
        gap: $spacing-2;
        overflow-x: auto;
        padding: $spacing-1 0;

        .thumbnail-item {
          flex-shrink: 0;
          width: 80px;
          height: 80px;
          border-radius: $border-radius-base;
          overflow: hidden;
          cursor: pointer;
          border: 2px solid transparent;
          transition: border-color $transition-fast $ease-out;

          &.active {
            border-color: $primary-color;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    // 商品信息区域
    .product-info {
      .product-title {
        font-size: $font-size-xxl;
        font-weight: $font-weight-bold;
        color: $text-primary;
        line-height: $line-height-tight;
        margin-bottom: $spacing-3;

        @include respond-to(md) {
          font-size: $font-size-xl;
        }
      }

      .product-subtitle {
        font-size: $font-size-md;
        color: $text-secondary;
        line-height: $line-height-relaxed;
        margin-bottom: $spacing-6;
      }

      .price-section {
        background: linear-gradient(135deg, $primary-light 0%, rgba(255, 107, 53, 0.1) 100%);
        padding: $spacing-4;
        border-radius: $border-radius-lg;
        margin-bottom: $spacing-6;

        .current-price {
          display: flex;
          align-items: baseline;
          margin-bottom: $spacing-2;

          .currency {
            font-size: $font-size-lg;
            color: $primary-color;
            font-weight: $font-weight-semibold;
            margin-right: $spacing-1;
          }

          .price {
            font-size: $font-size-xxxl;
            color: $primary-color;
            font-weight: $font-weight-black;
          }
        }

        .original-price {
          font-size: $font-size-base;
          color: $text-tertiary;
          text-decoration: line-through;
          margin-bottom: $spacing-1;
        }

        .discount {
          .discount-tag {
            background-color: $error-color;
            color: $text-white;
            padding: $spacing-1 $spacing-2;
            border-radius: $border-radius-base;
            font-size: $font-size-sm;
            font-weight: $font-weight-semibold;
          }
        }
      }

      .rating-section {
        display: flex;
        align-items: center;
        gap: $spacing-4;
        margin-bottom: $spacing-6;

        .rating-stars {
          display: flex;
          align-items: center;
          gap: $spacing-1;

          .star {
            font-size: $font-size-lg;
            color: #ddd;

            &.active {
              color: $warning-color;
            }
          }

          .rating-score {
            margin-left: $spacing-2;
            font-weight: $font-weight-semibold;
            color: $text-primary;
          }
        }

        .rating-count {
          color: $text-secondary;
          font-size: $font-size-sm;
        }
      }

      .specs-section {
        margin-bottom: $spacing-6;

        .spec-group {
          display: flex;
          align-items: flex-start;
          margin-bottom: $spacing-4;

          .spec-label {
            min-width: 80px;
            font-weight: $font-weight-semibold;
            color: $text-primary;
            padding-top: $spacing-2;
          }

          .spec-options {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            gap: $spacing-2;

            .spec-option {
              padding: $spacing-2 $spacing-4;
              border: 1px solid $border-medium;
              border-radius: $border-radius-base;
              background-color: $bg-primary;
              color: $text-primary;
              cursor: pointer;
              transition: all $transition-fast $ease-out;

              &:hover:not(:disabled) {
                border-color: $primary-color;
                color: $primary-color;
              }

              &.active {
                border-color: $primary-color;
                background-color: $primary-color;
                color: $text-white;
              }

              &.disabled {
                background-color: $bg-disabled;
                color: $text-tertiary;
                cursor: not-allowed;
              }
            }
          }
        }
      }

      .quantity-section {
        display: flex;
        align-items: center;
        gap: $spacing-4;
        margin-bottom: $spacing-6;

        .quantity-label {
          font-weight: $font-weight-semibold;
          color: $text-primary;
        }

        .quantity-control {
          display: flex;
          align-items: center;
          border: 1px solid $border-medium;
          border-radius: $border-radius-base;
          overflow: hidden;

          .quantity-btn {
            width: 40px;
            height: 40px;
            border: none;
            background-color: $bg-hover;
            color: $text-primary;
            cursor: pointer;
            transition: background-color $transition-fast $ease-out;

            &:hover:not(:disabled) {
              background-color: $primary-color;
              color: $text-white;
            }

            &:disabled {
              background-color: $bg-disabled;
              color: $text-tertiary;
              cursor: not-allowed;
            }
          }

          .quantity-input {
            width: 60px;
            height: 40px;
            border: none;
            text-align: center;
            font-size: $font-size-base;

            &:focus {
              outline: none;
            }
          }
        }

        .stock-info {
          color: $text-secondary;
          font-size: $font-size-sm;
        }
      }

      .action-buttons {
        display: flex;
        gap: $spacing-4;
        margin-bottom: $spacing-6;

        .btn {
          flex: 1;
          height: 50px;
          font-size: $font-size-md;
          font-weight: $font-weight-semibold;
          border-radius: $border-radius-lg;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: $spacing-2;

          .icon-cart {
            width: 20px;
            height: 20px;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMuMzMzMzMgMy4zMzMzM0g0LjE2NjY3TDYuNSAxMi41SDEzLjMzMzNMMTUuODMzMyA1SDUuODMzMzNNNi41IDE2LjY2NjdDNS44NSAxNi42NjY3IDUuMzMzMzMgMTcuMTgzMyA1LjMzMzMzIDE3LjgzMzNDNS4zMzMzMyAxOC40ODMzIDUuODUgMTkgNi41IDE5QzcuMTUgMTkgNy42NjY2NyAxOC40ODMzIDcuNjY2NjcgMTcuODMzM0M3LjY2NjY3IDE3LjE4MzMgNy4xNSAxNi42NjY3IDYuNSAxNi42NjY3Wk0xMy4zMzMzIDE2LjY2NjdDMTIuNjgzMyAxNi42NjY3IDEyLjE2NjcgMTcuMTgzMyAxMi4xNjY3IDE3LjgzMzNDMTIuMTY2NyAxOC40ODMzIDEyLjY4MzMgMTkgMTMuMzMzMyAxOUMxMy45ODMzIDE5IDE0LjUgMTguNDgzMyAxNC41IDE3LjgzMzNDMTQuNSAxNy4xODMzIDEzLjk4MzMgMTYuNjY2NyAxMy4zMzMzIDE2LjY2NjdaIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=') no-repeat center;
          }
        }

        @include respond-to(sm) {
          flex-direction: column;
        }
      }

      .service-section {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-3;
        padding: $spacing-4;
        background-color: $bg-tertiary;
        border-radius: $border-radius-lg;

        @include respond-to(sm) {
          grid-template-columns: 1fr;
        }

        .service-item {
          display: flex;
          align-items: center;
          gap: $spacing-2;
          font-size: $font-size-sm;
          color: $text-secondary;

          .service-icon {
            font-size: $font-size-base;
          }
        }
      }
    }
  }

  // 商品详细信息
  .product-details {
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    overflow: hidden;
    margin-bottom: $spacing-6;

    .detail-tabs {
      display: flex;
      border-bottom: 1px solid $border-light;

      .tab-btn {
        padding: $spacing-4 $spacing-6;
        border: none;
        background: none;
        font-size: $font-size-base;
        color: $text-secondary;
        cursor: pointer;
        transition: all $transition-fast $ease-out;
        border-bottom: 2px solid transparent;

        &:hover {
          color: $primary-color;
        }

        &.active {
          color: $primary-color;
          border-bottom-color: $primary-color;
          font-weight: $font-weight-semibold;
        }
      }
    }

    .tab-content {
      padding: $spacing-6;

      .description-content {
        h3 {
          font-size: $font-size-lg;
          font-weight: $font-weight-semibold;
          color: $text-primary;
          margin-bottom: $spacing-4;
        }

        p {
          color: $text-secondary;
          line-height: $line-height-relaxed;
          margin-bottom: $spacing-6;
        }

        .spec-table {
          width: 100%;
          border-collapse: collapse;

          tr {
            border-bottom: 1px solid $border-light;

            &:last-child {
              border-bottom: none;
            }
          }

          td {
            padding: $spacing-3 $spacing-4;

            &.spec-name {
              background-color: $bg-tertiary;
              font-weight: $font-weight-semibold;
              color: $text-primary;
              width: 150px;
            }

            &.spec-value {
              color: $text-secondary;
            }
          }
        }
      }

      .reviews-content {
        .reviews-summary {
          margin-bottom: $spacing-6;

          .rating-overview {
            display: flex;
            align-items: center;
            gap: $spacing-4;
            padding: $spacing-4;
            background-color: $bg-tertiary;
            border-radius: $border-radius-lg;

            .rating-score-large {
              font-size: $font-size-xxxl;
              font-weight: $font-weight-black;
              color: $primary-color;
            }

            .rating-stars-large {
              .star {
                font-size: $font-size-xl;
                color: #ddd;
                margin-right: $spacing-1;

                &.active {
                  color: $warning-color;
                }
              }
            }

            .rating-count {
              color: $text-secondary;
              font-size: $font-size-base;
            }
          }
        }

        .reviews-list {
          .review-item {
            padding: $spacing-4 0;
            border-bottom: 1px solid $border-light;

            &:last-child {
              border-bottom: none;
            }

            .review-header {
              display: flex;
              align-items: center;
              gap: $spacing-3;
              margin-bottom: $spacing-3;

              .user-avatar {
                width: 40px;
                height: 40px;
                border-radius: $border-radius-round;
                object-fit: cover;
              }

              .user-info {
                flex: 1;

                .user-name {
                  font-weight: $font-weight-semibold;
                  color: $text-primary;
                  margin-bottom: $spacing-1;
                }

                .review-rating {
                  .star {
                    font-size: $font-size-sm;
                    color: #ddd;

                    &.active {
                      color: $warning-color;
                    }
                  }
                }
              }

              .review-date {
                color: $text-tertiary;
                font-size: $font-size-sm;
              }
            }

            .review-content {
              color: $text-secondary;
              line-height: $line-height-relaxed;
              margin-bottom: $spacing-3;
            }

            .review-images {
              display: flex;
              gap: $spacing-2;
              margin-bottom: $spacing-3;

              .review-image {
                width: 80px;
                height: 80px;
                border-radius: $border-radius-base;
                object-fit: cover;
                cursor: pointer;
                transition: transform $transition-fast $ease-out;

                &:hover {
                  transform: scale(1.1);
                }
              }
            }

            .review-helpful {
              .helpful-btn {
                background: none;
                border: 1px solid $border-medium;
                padding: $spacing-1 $spacing-3;
                border-radius: $border-radius-base;
                color: $text-secondary;
                cursor: pointer;
                transition: all $transition-fast $ease-out;

                &:hover {
                  border-color: $primary-color;
                  color: $primary-color;
                }
              }
            }
          }
        }
      }
    }
  }

  // 相关商品推荐
  .related-products {
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    padding: $spacing-6;

    .section-title {
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-6;
      text-align: center;
    }

    .related-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: $spacing-4;

      @include respond-to(sm) {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }

  // 商品不存在
  .product-not-found {
    text-align: center;
    padding: $spacing-16 $spacing-6;
    background-color: $bg-primary;
    border-radius: $border-radius-lg;

    .not-found-icon {
      font-size: 80px;
      margin-bottom: $spacing-6;
      opacity: 0.5;
    }

    h3 {
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-3;
    }

    p {
      color: $text-secondary;
      margin-bottom: $spacing-6;
    }
  }

  // 图片查看器
  .image-viewer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .viewer-content {
      position: relative;
      max-width: 90vw;
      max-height: 90vh;

      .viewer-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }

      .close-btn {
        position: absolute;
        top: -40px;
        right: 0;
        background: none;
        border: none;
        color: $text-white;
        font-size: 30px;
        cursor: pointer;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: $border-radius-round;
        }
      }

      .nav-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-color: rgba(0, 0, 0, 0.5);
        border: none;
        color: $text-white;
        font-size: 30px;
        width: 50px;
        height: 50px;
        border-radius: $border-radius-round;
        cursor: pointer;
        transition: background-color $transition-fast $ease-out;

        &:hover {
          background-color: rgba(0, 0, 0, 0.7);
        }

        &.prev-btn {
          left: -60px;
        }

        &.next-btn {
          right: -60px;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
