<template>
  <header class="main-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo区域 -->
        <div class="logo-section">
          <a href="/" class="logo-link">
            <img src="/logo.png" alt="京东" class="logo-img" />
          </a>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-section">
          <div class="search-box">
            <input 
              type="text" 
              class="search-input" 
              placeholder="搜索商品、品牌或店铺"
              v-model="searchKeyword"
              @keyup.enter="handleSearch"
            />
            <button class="search-btn" @click="handleSearch">
              <i class="icon-search"></i>
              搜索
            </button>
          </div>
          
          <!-- 热门搜索词 -->
          <div class="hot-keywords" v-if="showHotKeywords">
            <span class="hot-label">热门：</span>
            <a 
              href="#" 
              class="hot-keyword" 
              v-for="keyword in hotKeywords" 
              :key="keyword"
              @click="searchKeyword = keyword"
            >
              {{ keyword }}
            </a>
          </div>
        </div>
        
        <!-- 用户操作区域 -->
        <div class="user-section">
          <!-- 购物车 -->
          <div class="cart-wrapper">
            <a href="#" class="cart-link">
              <i class="icon-cart"></i>
              <span class="cart-text">购物车</span>
              <span class="cart-count" v-if="cartCount > 0">{{ cartCount }}</span>
            </a>
          </div>
          
          <!-- 移动端菜单按钮 -->
          <button class="mobile-menu-btn" @click="toggleMobileMenu">
            <span class="menu-line"></span>
            <span class="menu-line"></span>
            <span class="menu-line"></span>
          </button>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
export default {
  name: 'Header',
  data() {
    return {
      searchKeyword: '',
      cartCount: 0,
      showHotKeywords: true,
      hotKeywords: ['iPhone 15', '华为手机', '小米电视', '戴森吸尘器', '耐克运动鞋'],
      showMobileMenu: false
    }
  },
  methods: {
    handleSearch() {
      if (this.searchKeyword.trim()) {
        console.log('搜索:', this.searchKeyword)
        // 这里可以添加搜索逻辑
      }
    },
    toggleMobileMenu() {
      this.showMobileMenu = !this.showMobileMenu
    }
  }
}
</script>

<style lang="scss" scoped>
.main-header {
  height: $header-height;
  background-color: $bg-primary;
  box-shadow: $box-shadow-sm;
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  
  .header-content {
    display: flex;
    align-items: center;
    height: 100%;
    gap: $spacing-lg;
  }
  
  // Logo区域
  .logo-section {
    flex-shrink: 0;
    width: 120px;
    
    .logo-link {
      display: block;
      
      .logo-img {
        height: 40px;
        width: auto;
      }
    }
  }
  
  // 搜索区域
  .search-section {
    flex: 1;
    max-width: 600px;
    position: relative;
    
    .search-box {
      display: flex;
      border: 2px solid $primary-color;
      border-radius: $border-radius-base;
      overflow: hidden;
      
      .search-input {
        flex: 1;
        padding: $spacing-sm $spacing-base;
        border: none;
        font-size: $font-size-sm;
        outline: none;
        
        &::placeholder {
          color: $text-placeholder;
        }
      }
      
      .search-btn {
        padding: $spacing-sm $spacing-lg;
        background-color: $primary-color;
        color: $bg-primary;
        border: none;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
        cursor: pointer;
        transition: background-color $transition-fast $ease-out;
        display: flex;
        align-items: center;
        gap: 4px;
        
        &:hover {
          background-color: $primary-hover;
        }
        
        .icon-search {
          width: 16px;
          height: 16px;
          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTJDOS43NjE0MiAxMiAxMiA5Ljc2MTQyIDEyIDdDMTIgNC4yMzg1OCA5Ljc2MTQyIDIgNyAyQzQuMjM4NTggMiAyIDQuMjM4NTggMiA3QzIgOS43NjE0MiA0LjIzODU4IDEyIDcgMTJaIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0xMC41IDEwLjVMMTQgMTQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+') no-repeat center;
        }
      }
    }
    
    .hot-keywords {
      margin-top: $spacing-sm;
      font-size: $font-size-xs;
      
      .hot-label {
        color: $text-tertiary;
        margin-right: $spacing-sm;
      }
      
      .hot-keyword {
        color: $text-secondary;
        margin-right: $spacing-base;
        text-decoration: none;
        
        &:hover {
          color: $primary-color;
        }
      }
    }
  }
  
  // 用户操作区域
  .user-section {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: $spacing-md;
    
    .cart-wrapper {
      position: relative;
      
      .cart-link {
        display: flex;
        align-items: center;
        gap: 4px;
        color: $text-primary;
        text-decoration: none;
        font-size: $font-size-sm;
        transition: color $transition-fast $ease-out;
        
        &:hover {
          color: $primary-color;
        }
        
        .icon-cart {
          width: 20px;
          height: 20px;
          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMuMzMzMzMgMy4zMzMzM0g0LjE2NjY3TDYuNSAxMi41SDEzLjMzMzNMMTUuODMzMyA1SDUuODMzMzNNNi41IDE2LjY2NjdDNS44NSAxNi42NjY3IDUuMzMzMzMgMTcuMTgzMyA1LjMzMzMzIDE3LjgzMzNDNS4zMzMzMyAxOC40ODMzIDUuODUgMTkgNi41IDE5QzcuMTUgMTkgNy42NjY2NyAxOC40ODMzIDcuNjY2NjcgMTcuODMzM0M3LjY2NjY3IDE3LjE4MzMgNy4xNSAxNi42NjY3IDYuNSAxNi42NjY3Wk0xMy4zMzMzIDE2LjY2NjdDMTIuNjgzMyAxNi42NjY3IDEyLjE2NjcgMTcuMTgzMyAxMi4xNjY3IDE3LjgzMzNDMTIuMTY2NyAxOC40ODMzIDEyLjY4MzMgMTkgMTMuMzMzMyAxOUMxMy45ODMzIDE5IDE0LjUgMTguNDgzMyAxNC41IDE3LjgzMzNDMTQuNSAxNy4xODMzIDEzLjk4MzMgMTYuNjY2NyAxMy4zMzMzIDE2LjY2NjdaIiBzdHJva2U9IiMzMzMzMzMiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+') no-repeat center;
        }
        
        .cart-count {
          position: absolute;
          top: -8px;
          right: -8px;
          background-color: $primary-color;
          color: $bg-primary;
          font-size: 10px;
          font-weight: $font-weight-bold;
          padding: 2px 6px;
          border-radius: $border-radius-round;
          min-width: 18px;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    
    // 移动端菜单按钮
    .mobile-menu-btn {
      display: none;
      flex-direction: column;
      gap: 3px;
      background: none;
      border: none;
      padding: $spacing-xs;
      cursor: pointer;
      
      .menu-line {
        width: 20px;
        height: 2px;
        background-color: $text-primary;
        transition: all $transition-fast $ease-out;
      }
      
      @include respond-to(xs) {
        display: flex;
      }
    }
  }
  
  // 移动端适配
  @include respond-to(xs) {
    .header-content {
      gap: $spacing-sm;
    }
    
    .logo-section {
      width: 80px;
      
      .logo-img {
        height: 30px;
      }
    }
    
    .search-section {
      .hot-keywords {
        display: none;
      }
    }
    
    .user-section {
      .cart-link .cart-text {
        display: none;
      }
    }
  }
}
</style>
