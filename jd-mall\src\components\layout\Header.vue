<template>
  <header class="main-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo区域 -->
        <div class="logo-section">
          <a href="/" class="logo-link">
            <img src="/logo.png" alt="京东" class="logo-img" />
          </a>
        </div>
        
        <!-- 分类和搜索区域 -->
        <div class="search-section">
          <!-- 分类下拉菜单 -->
          <CategoryDropdown />

          <!-- 搜索框 -->
          <div class="search-box">
            <input
              type="text"
              class="search-input"
              placeholder="请输入您要搜索的商品名称"
              v-model="searchKeyword"
              @keyup.enter="handleSearch"
              @input="handleSearchInput"
              @focus="showSuggestions = true"
              @blur="hideSuggestions"
            />
            <button class="search-btn" @click="handleSearch">
              搜索
            </button>

            <!-- 搜索建议 -->
            <div class="search-suggestions" v-if="showSuggestions && suggestions.length">
              <div
                class="suggestion-item"
                v-for="suggestion in suggestions"
                :key="suggestion"
                @mousedown="selectSuggestion(suggestion)"
              >
                <i class="search-icon">🔍</i>
                {{ suggestion }}
              </div>
            </div>
          </div>

          <!-- 热门搜索词 -->
          <div class="hot-keywords" v-if="showHotKeywords && !showSuggestions">
            <span class="hot-label">热门：</span>
            <a
              href="#"
              class="hot-keyword"
              v-for="keyword in hotKeywords"
              :key="keyword"
              @click="selectSuggestion(keyword)"
            >
              {{ keyword }}
            </a>
          </div>
        </div>
        
        <!-- 用户操作区域 -->
        <div class="user-section">
          <!-- 购物车 -->
          <div class="cart-wrapper">
            <router-link to="/cart" class="cart-link">
              <i class="icon-cart"></i>
              <span class="cart-text">购物车</span>
              <span class="cart-count" v-if="cartCount > 0">{{ cartCount }}</span>
            </router-link>
          </div>
          
          <!-- 移动端菜单按钮 -->
          <button class="mobile-menu-btn" @click="toggleMobileMenu">
            <span class="menu-line"></span>
            <span class="menu-line"></span>
            <span class="menu-line"></span>
          </button>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { useRouter } from 'vue-router'
import { useCartStore } from '../../stores/cart.js'
import { useProductStore } from '../../stores/product.js'
import CategoryDropdown from '../common/CategoryDropdown.vue'

export default {
  name: 'Header',
  components: {
    CategoryDropdown
  },
  setup() {
    const router = useRouter()
    const cartStore = useCartStore()
    const productStore = useProductStore()

    return {
      router,
      cartStore,
      productStore
    }
  },
  data() {
    return {
      searchKeyword: '',
      showHotKeywords: true,
      showSuggestions: false,
      suggestions: [],
      hotKeywords: ['iPhone 15', '华为手机', '小米电视', '戴森吸尘器', '耐克运动鞋'],
      showMobileMenu: false,
      searchTimer: null,
      showCategoryMenu: false,
      activeCategory: null
    }
  },
  computed: {
    cartCount() {
      return this.cartStore.itemCount
    },
    categories() {
      return this.productStore.categories
    }
  },
  methods: {
    handleSearch() {
      if (this.searchKeyword.trim()) {
        this.router.push({
          name: 'Search',
          query: { q: this.searchKeyword.trim() }
        })
        this.showSuggestions = false
      }
    },

    handleSearchInput() {
      clearTimeout(this.searchTimer)

      if (this.searchKeyword.trim()) {
        this.searchTimer = setTimeout(() => {
          this.suggestions = this.productStore.getSearchSuggestions(this.searchKeyword)
          this.showSuggestions = this.suggestions.length > 0
        }, 300)
      } else {
        this.showSuggestions = false
        this.suggestions = []
      }
    },

    selectSuggestion(suggestion) {
      this.searchKeyword = suggestion
      this.handleSearch()
    },

    hideSuggestions() {
      setTimeout(() => {
        this.showSuggestions = false
      }, 200)
    },

    goToCategory(categoryId, subcategoryId = null) {
      if (subcategoryId) {
        this.router.push(`/category/${categoryId}?sub=${subcategoryId}`)
      } else {
        this.router.push(`/category/${categoryId}`)
      }
      this.showCategoryMenu = false
    },

    toggleMobileMenu() {
      this.showMobileMenu = !this.showMobileMenu
    }
  }
}
</script>

<style lang="scss" scoped>
.main-header {
  height: $header-height;
  background-color: $bg-primary;
  box-shadow: $box-shadow-sm;
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  
  .header-content {
    display: flex;
    align-items: center;
    height: 100%;
    gap: $spacing-lg;
  }
  
  // Logo区域
  .logo-section {
    flex-shrink: 0;
    width: 120px;
    
    .logo-link {
      display: block;
      
      .logo-img {
        height: 40px;
        width: auto;
      }
    }
  }
  
  // 搜索区域
  .search-section {
    flex: 1;
    max-width: 800px;
    position: relative;
    display: flex;
    gap: 0;

    @include respond-to(md) {
      max-width: none;
      margin: $spacing-4 0;
      order: 3;
      width: 100%;
      flex-direction: column;
    }

    .search-box {
      flex: 1;
      display: flex;
      border: 2px solid $primary-color;
      border-left: none;
      border-radius: 0 $border-radius-base $border-radius-base 0;
      overflow: hidden;
      position: relative;

      @include respond-to(md) {
        border-left: 2px solid $primary-color;
        border-radius: $border-radius-base;
      }

      .search-input {
        flex: 1;
        padding: $spacing-3 $spacing-4;
        border: none;
        font-size: $font-size-sm;
        outline: none;

        &::placeholder {
          color: $text-placeholder;
        }
      }

      .search-btn {
        padding: $spacing-3 $spacing-6;
        background-color: $primary-color;
        color: $text-white;
        border: none;
        font-size: $font-size-sm;
        font-weight: $font-weight-semibold;
        cursor: pointer;
        transition: background-color $transition-fast $ease-out;

        &:hover {
          background-color: $primary-hover;
        }
      }
    }

      .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: $bg-primary;
        border: 1px solid $border-medium;
        border-top: none;
        border-radius: 0 0 $border-radius-base $border-radius-base;
        box-shadow: $box-shadow-md;
        z-index: $z-index-dropdown;
        max-height: 300px;
        overflow-y: auto;

        .suggestion-item {
          display: flex;
          align-items: center;
          gap: $spacing-2;
          padding: $spacing-3 $spacing-4;
          cursor: pointer;
          transition: background-color $transition-fast $ease-out;

          &:hover {
            background-color: $bg-hover;
          }

          &:last-child {
            border-radius: 0 0 $border-radius-base $border-radius-base;
          }

          .search-icon {
            color: $text-tertiary;
            font-size: $font-size-sm;
          }
        }
      }
    }

    .hot-keywords {
      margin-top: $spacing-sm;
      font-size: $font-size-xs;

      .hot-label {
        color: $text-tertiary;
        margin-right: $spacing-sm;
      }

      .hot-keyword {
        color: $text-secondary;
        margin-right: $spacing-base;
        text-decoration: none;
        cursor: pointer;

        &:hover {
          color: $primary-color;
        }
      }
    }
  }

  // 用户操作区域
  .user-section {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: $spacing-md;
    
    .cart-wrapper {
      position: relative;
      
      .cart-link {
        display: flex;
        align-items: center;
        gap: 4px;
        color: $text-primary;
        text-decoration: none;
        font-size: $font-size-sm;
        transition: color $transition-fast $ease-out;
        
        &:hover {
          color: $primary-color;
        }
        
        .icon-cart {
          width: 20px;
          height: 20px;
          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMuMzMzMzMgMy4zMzMzM0g0LjE2NjY3TDYuNSAxMi41SDEzLjMzMzNMMTUuODMzMyA1SDUuODMzMzNNNi41IDE2LjY2NjdDNS44NSAxNi42NjY3IDUuMzMzMzMgMTcuMTgzMyA1LjMzMzMzIDE3LjgzMzNDNS4zMzMzMyAxOC40ODMzIDUuODUgMTkgNi41IDE5QzcuMTUgMTkgNy42NjY2NyAxOC40ODMzIDcuNjY2NjcgMTcuODMzM0M3LjY2NjY3IDE3LjE4MzMgNy4xNSAxNi42NjY3IDYuNSAxNi42NjY3Wk0xMy4zMzMzIDE2LjY2NjdDMTIuNjgzMyAxNi42NjY3IDEyLjE2NjcgMTcuMTgzMyAxMi4xNjY3IDE3LjgzMzNDMTIuMTY2NyAxOC40ODMzIDEyLjY4MzMgMTkgMTMuMzMzMyAxOUMxMy45ODMzIDE5IDE0LjUgMTguNDgzMyAxNC41IDE3LjgzMzNDMTQuNSAxNy4xODMzIDEzLjk4MzMgMTYuNjY2NyAxMy4zMzMzIDE2LjY2NjdaIiBzdHJva2U9IiMzMzMzMzMiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+') no-repeat center;
        }
        
        .cart-count {
          position: absolute;
          top: -8px;
          right: -8px;
          background-color: $primary-color;
          color: $bg-primary;
          font-size: 10px;
          font-weight: $font-weight-bold;
          padding: 2px 6px;
          border-radius: $border-radius-round;
          min-width: 18px;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    
    // 移动端菜单按钮
    .mobile-menu-btn {
      display: none;
      flex-direction: column;
      gap: 3px;
      background: none;
      border: none;
      padding: $spacing-xs;
      cursor: pointer;
      
      .menu-line {
        width: 20px;
        height: 2px;
        background-color: $text-primary;
        transition: all $transition-fast $ease-out;
      }
      
      @include respond-to(xs) {
        display: flex;
      }
    }
  }
  
  // 移动端适配
  @include respond-to(xs) {
    .header-content {
      gap: $spacing-sm;
    }
    
    .logo-section {
      width: 80px;
      
      .logo-img {
        height: 30px;
      }
    }
    
    .search-section {
      .hot-keywords {
        display: none;
      }
    }
    
    .user-section {
      .cart-link .cart-text {
        display: none;
      }
    }
  }
}
</style>
