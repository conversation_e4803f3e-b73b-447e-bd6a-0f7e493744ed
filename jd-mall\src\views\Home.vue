<template>
  <div class="home-page">
    <!-- 主要内容区域 -->
    <div class="container">
      <div class="home-content">
        <!-- 左侧分类导航 -->
        <aside class="category-sidebar">
          <div class="category-menu">
            <div class="category-title">
              <i class="menu-icon">☰</i>
              全部商品分类
            </div>
            <ul class="category-list">
              <li 
                class="category-item" 
                v-for="category in categories" 
                :key="category.id"
                @mouseenter="showSubCategory(category)"
                @mouseleave="hideSubCategory"
              >
                <a href="#" class="category-link">
                  <span class="category-icon">{{ category.icon }}</span>
                  <span class="category-name">{{ category.name }}</span>
                  <i class="arrow-right">›</i>
                </a>
                
                <!-- 二级分类 -->
                <div 
                  class="sub-category" 
                  v-if="activeCategory && activeCategory.id === category.id"
                >
                  <div class="sub-category-content">
                    <div class="sub-category-list">
                      <a 
                        href="#" 
                        class="sub-category-item"
                        v-for="subCat in category.children"
                        :key="subCat.id"
                      >
                        {{ subCat.name }}
                        <span class="item-count">({{ subCat.count }})</span>
                      </a>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </aside>
        
        <!-- 右侧主要内容 -->
        <main class="main-content">
          <!-- 轮播图区域 -->
          <section class="banner-section">
            <Carousel 
              :items="banners" 
              :height="'400px'"
              :auto-play="true"
              :interval="5000"
            />
          </section>
          
          <!-- 促销活动区域 -->
          <section class="promotion-section">
            <div class="promotion-grid">
              <div 
                class="promotion-item" 
                v-for="promo in promotions" 
                :key="promo.id"
              >
                <a :href="promo.link" class="promotion-link">
                  <img :src="promo.image" :alt="promo.title" class="promotion-image" />
                  <div class="promotion-info">
                    <h4 class="promotion-title">{{ promo.title }}</h4>
                    <p class="promotion-subtitle">{{ promo.subtitle }}</p>
                  </div>
                </a>
              </div>
            </div>
          </section>
        </main>
      </div>
      
      <!-- 商品推荐区域 -->
      <section class="products-section">
        <div class="section-header">
          <h2 class="section-title">为你推荐</h2>
          <div class="section-tabs">
            <button 
              class="tab-btn" 
              :class="{ active: activeTab === tab.key }"
              v-for="tab in productTabs" 
              :key="tab.key"
              @click="switchTab(tab.key)"
            >
              {{ tab.label }}
            </button>
          </div>
        </div>
        
        <ProductGrid 
          :products="displayProducts"
          :loading="productsLoading"
          @product-click="handleProductClick"
          @add-to-cart="handleAddToCart"
        />
        
        <!-- 加载更多按钮 -->
        <div class="load-more-section" v-if="hasMore">
          <button 
            class="btn btn-secondary load-more-btn" 
            @click="loadMoreProducts"
            :disabled="productsLoading"
          >
            {{ productsLoading ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import Carousel from '../components/common/Carousel.vue'
import ProductGrid from '../components/common/ProductGrid.vue'
import { bannerData, productData, categoryData, promotionData } from '../data/mockData.js'

export default {
  name: 'Home',
  components: {
    Carousel,
    ProductGrid
  },
  data() {
    return {
      banners: bannerData,
      categories: categoryData,
      promotions: promotionData,
      products: productData,
      displayProducts: [],
      activeCategory: null,
      activeTab: 'recommend',
      productsLoading: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 8,
      productTabs: [
        { key: 'recommend', label: '推荐' },
        { key: 'new', label: '新品' },
        { key: 'hot', label: '热销' },
        { key: 'price', label: '价格' }
      ]
    }
  },
  mounted() {
    this.loadProducts()
  },
  methods: {
    showSubCategory(category) {
      this.activeCategory = category
    },
    
    hideSubCategory() {
      // 延迟隐藏，避免鼠标移动时闪烁
      setTimeout(() => {
        this.activeCategory = null
      }, 200)
    },
    
    switchTab(tabKey) {
      this.activeTab = tabKey
      this.currentPage = 1
      this.displayProducts = []
      this.hasMore = true
      this.loadProducts()
    },
    
    loadProducts() {
      this.productsLoading = true
      
      // 模拟API请求延迟
      setTimeout(() => {
        const startIndex = (this.currentPage - 1) * this.pageSize
        const endIndex = startIndex + this.pageSize
        const newProducts = this.products.slice(startIndex, endIndex)
        
        if (this.currentPage === 1) {
          this.displayProducts = newProducts
        } else {
          this.displayProducts.push(...newProducts)
        }
        
        this.hasMore = endIndex < this.products.length
        this.productsLoading = false
      }, 800)
    },
    
    loadMoreProducts() {
      this.currentPage++
      this.loadProducts()
    },
    
    handleProductClick(product) {
      console.log('点击商品:', product)
      // 这里可以跳转到商品详情页
    },
    
    handleAddToCart(product) {
      console.log('添加到购物车:', product)
      // 这里可以添加购物车逻辑
      this.$message?.success?.(`${product.title} 已添加到购物车`)
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  .home-content {
    display: flex;
    gap: $spacing-lg;
    margin-bottom: $spacing-xl;
    
    @include respond-to(md) {
      flex-direction: column;
      gap: $spacing-base;
    }
  }
  
  // 左侧分类导航
  .category-sidebar {
    width: $sidebar-width;
    flex-shrink: 0;
    
    @include respond-to(md) {
      width: 100%;
    }
    
    .category-menu {
      @include card-base;
      overflow: hidden;
      
      .category-title {
        background-color: $primary-color;
        color: $bg-primary;
        padding: $spacing-base;
        font-weight: $font-weight-semibold;
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        
        .menu-icon {
          font-size: $font-size-lg;
        }
      }
      
      .category-list {
        .category-item {
          position: relative;
          border-bottom: 1px solid $border-light;
          
          &:last-child {
            border-bottom: none;
          }
          
          &:hover {
            background-color: $bg-hover;
          }
          
          .category-link {
            display: flex;
            align-items: center;
            padding: $spacing-sm $spacing-base;
            color: $text-primary;
            text-decoration: none;
            transition: all $transition-fast $ease-out;
            
            .category-icon {
              margin-right: $spacing-sm;
              font-size: $font-size-base;
            }
            
            .category-name {
              flex: 1;
              font-size: $font-size-sm;
            }
            
            .arrow-right {
              color: $text-tertiary;
              font-size: $font-size-sm;
            }
          }
          
          // 二级分类
          .sub-category {
            position: absolute;
            left: 100%;
            top: 0;
            width: 400px;
            background-color: $bg-primary;
            box-shadow: $box-shadow-lg;
            border-radius: $border-radius-base;
            z-index: $z-index-dropdown;
            
            @include respond-to(md) {
              position: static;
              width: 100%;
              box-shadow: none;
              border-radius: 0;
              border-top: 1px solid $border-light;
            }
            
            .sub-category-content {
              padding: $spacing-base;
              
              .sub-category-list {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: $spacing-xs;
                
                @include respond-to(md) {
                  grid-template-columns: 1fr;
                }
                
                .sub-category-item {
                  display: block;
                  padding: $spacing-xs $spacing-sm;
                  color: $text-secondary;
                  text-decoration: none;
                  font-size: $font-size-xs;
                  border-radius: $border-radius-sm;
                  transition: all $transition-fast $ease-out;
                  
                  &:hover {
                    background-color: $bg-hover;
                    color: $primary-color;
                  }
                  
                  .item-count {
                    color: $text-tertiary;
                    font-size: 10px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
  // 右侧主要内容
  .main-content {
    flex: 1;
    
    .banner-section {
      margin-bottom: $spacing-lg;
    }
    
    .promotion-section {
      .promotion-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: $spacing-base;
        
        @include respond-to(md) {
          grid-template-columns: repeat(2, 1fr);
        }
        
        @include respond-to(xs) {
          grid-template-columns: 1fr;
        }
        
        .promotion-item {
          @include card-base;
          overflow: hidden;
          
          .promotion-link {
            display: block;
            text-decoration: none;
            
            .promotion-image {
              width: 100%;
              height: 120px;
              object-fit: cover;
            }
            
            .promotion-info {
              padding: $spacing-sm;
              
              .promotion-title {
                font-size: $font-size-sm;
                font-weight: $font-weight-semibold;
                color: $text-primary;
                margin-bottom: 2px;
              }
              
              .promotion-subtitle {
                font-size: $font-size-xs;
                color: $text-secondary;
                margin: 0;
              }
            }
          }
        }
      }
    }
  }
  
  // 商品推荐区域
  .products-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-lg;
      
      @include respond-to(sm) {
        flex-direction: column;
        gap: $spacing-base;
        align-items: flex-start;
      }
      
      .section-title {
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: $text-primary;
        margin: 0;
      }
      
      .section-tabs {
        display: flex;
        gap: $spacing-base;
        
        .tab-btn {
          padding: $spacing-xs $spacing-base;
          border: 1px solid $border-light;
          background-color: $bg-primary;
          color: $text-secondary;
          font-size: $font-size-sm;
          border-radius: $border-radius-base;
          cursor: pointer;
          transition: all $transition-fast $ease-out;
          
          &:hover {
            border-color: $primary-color;
            color: $primary-color;
          }
          
          &.active {
            background-color: $primary-color;
            border-color: $primary-color;
            color: $bg-primary;
          }
        }
      }
    }
    
    .load-more-section {
      text-align: center;
      margin-top: $spacing-xl;
      
      .load-more-btn {
        padding: $spacing-base $spacing-xl;
        font-size: $font-size-sm;
      }
    }
  }
}
</style>
