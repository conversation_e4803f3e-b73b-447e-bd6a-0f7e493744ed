<template>
  <div class="not-found-page">
    <div class="container">
      <div class="not-found-content">
        <div class="error-code">404</div>
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被删除
        </p>
        
        <div class="error-actions">
          <button class="btn btn-primary" @click="$router.push('/')">
            返回首页
          </button>
          <button class="btn btn-secondary" @click="$router.back()">
            返回上页
          </button>
        </div>
        
        <div class="suggestions">
          <h3>您可能想要：</h3>
          <ul class="suggestion-list">
            <li><router-link to="/">浏览首页</router-link></li>
            <li><router-link to="/products">查看商品</router-link></li>
            <li><router-link to="/cart">查看购物车</router-link></li>
            <li><router-link to="/orders">我的订单</router-link></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotFound'
}
</script>

<style lang="scss" scoped>
.not-found-page {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-light 0%, $secondary-light 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-6;
  
  .not-found-content {
    text-align: center;
    background-color: $bg-primary;
    border-radius: $border-radius-xl;
    padding: $spacing-16 $spacing-8;
    box-shadow: $box-shadow-lg;
    max-width: 500px;
    width: 100%;
    
    .error-code {
      font-size: 120px;
      font-weight: $font-weight-black;
      color: $primary-color;
      line-height: 1;
      margin-bottom: $spacing-4;
      
      @include respond-to(sm) {
        font-size: 80px;
      }
    }
    
    .error-title {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-4;
    }
    
    .error-description {
      color: $text-secondary;
      font-size: $font-size-base;
      line-height: $line-height-relaxed;
      margin-bottom: $spacing-8;
    }
    
    .error-actions {
      display: flex;
      gap: $spacing-4;
      justify-content: center;
      margin-bottom: $spacing-8;
      
      @include respond-to(sm) {
        flex-direction: column;
      }
    }
    
    .suggestions {
      text-align: left;
      
      h3 {
        font-size: $font-size-lg;
        font-weight: $font-weight-semibold;
        color: $text-primary;
        margin-bottom: $spacing-4;
        text-align: center;
      }
      
      .suggestion-list {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
          margin-bottom: $spacing-2;
          
          a {
            color: $primary-color;
            text-decoration: none;
            transition: color $transition-fast $ease-out;
            
            &:hover {
              color: $primary-hover;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
</style>
