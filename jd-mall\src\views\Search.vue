<template>
  <div class="search-page">
    <div class="container">
      <div class="search-header">
        <div class="search-info">
          <h1 class="search-title">
            搜索结果
            <span v-if="searchKeyword" class="keyword">"{{ searchKeyword }}"</span>
          </h1>
          <div class="result-count">
            找到 {{ searchResults.length }} 个相关商品
          </div>
        </div>
      </div>

      <div class="search-content">
        <!-- 搜索筛选器 -->
        <div class="search-filters">
          <div class="filter-group">
            <label>价格：</label>
            <div class="filter-options">
              <button 
                class="filter-btn"
                :class="{ active: filters.priceRange === 'all' }"
                @click="updateFilter('priceRange', 'all')"
              >
                不限
              </button>
              <button 
                class="filter-btn"
                :class="{ active: filters.priceRange === '0-100' }"
                @click="updateFilter('priceRange', '0-100')"
              >
                0-100元
              </button>
              <button 
                class="filter-btn"
                :class="{ active: filters.priceRange === '100-500' }"
                @click="updateFilter('priceRange', '100-500')"
              >
                100-500元
              </button>
              <button 
                class="filter-btn"
                :class="{ active: filters.priceRange === '500+' }"
                @click="updateFilter('priceRange', '500+')"
              >
                500元以上
              </button>
            </div>
          </div>

          <div class="filter-group">
            <label>排序：</label>
            <div class="filter-options">
              <button 
                class="filter-btn"
                :class="{ active: filters.sortBy === 'default' }"
                @click="updateFilter('sortBy', 'default')"
              >
                综合
              </button>
              <button 
                class="filter-btn"
                :class="{ active: filters.sortBy === 'price-asc' }"
                @click="updateFilter('sortBy', 'price-asc')"
              >
                价格↑
              </button>
              <button 
                class="filter-btn"
                :class="{ active: filters.sortBy === 'price-desc' }"
                @click="updateFilter('sortBy', 'price-desc')"
              >
                价格↓
              </button>
              <button 
                class="filter-btn"
                :class="{ active: filters.sortBy === 'sales' }"
                @click="updateFilter('sortBy', 'sales')"
              >
                销量
              </button>
            </div>
          </div>
        </div>

        <!-- 搜索结果 -->
        <div class="search-results">
          <ProductGrid 
            :products="filteredResults"
            :loading="isLoading"
            :empty-text="searchKeyword ? `没有找到与"${searchKeyword}"相关的商品` : '请输入搜索关键词'"
            @product-click="goToProduct"
            @add-to-cart="handleAddToCart"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductStore } from '../stores/product.js'
import { useCartStore } from '../stores/cart.js'
import ProductGrid from '../components/common/ProductGrid.vue'

export default {
  name: 'Search',
  components: {
    ProductGrid
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const productStore = useProductStore()
    const cartStore = useCartStore()

    const isLoading = ref(false)
    const filters = ref({
      priceRange: 'all',
      sortBy: 'default'
    })

    const searchKeyword = computed(() => productStore.searchKeyword)
    const searchResults = computed(() => productStore.searchResults)

    const filteredResults = computed(() => {
      let results = [...searchResults.value]

      // 价格筛选
      if (filters.value.priceRange !== 'all') {
        const [min, max] = filters.value.priceRange.split('-').map(Number)
        if (max) {
          results = results.filter(product => 
            product.price >= min && product.price <= max
          )
        } else {
          results = results.filter(product => product.price >= min)
        }
      }

      // 排序
      switch (filters.value.sortBy) {
        case 'price-asc':
          results.sort((a, b) => a.price - b.price)
          break
        case 'price-desc':
          results.sort((a, b) => b.price - a.price)
          break
        case 'sales':
          results.sort((a, b) => (b.sales || 0) - (a.sales || 0))
          break
        default:
          // 默认排序
          break
      }

      return results
    })

    const updateFilter = (key, value) => {
      filters.value[key] = value
    }

    const goToProduct = (product) => {
      router.push(`/product/${product.id}`)
    }

    const handleAddToCart = (product) => {
      cartStore.addToCart(product, 1)
    }

    const performSearch = async (keyword) => {
      if (!keyword) return
      
      isLoading.value = true
      try {
        await productStore.searchProducts(keyword)
      } finally {
        isLoading.value = false
      }
    }

    // 监听路由查询参数变化
    watch(() => route.query.q, (newKeyword) => {
      if (newKeyword) {
        performSearch(newKeyword)
      }
    }, { immediate: true })

    onMounted(() => {
      const keyword = route.query.q
      if (keyword) {
        performSearch(keyword)
      }
    })

    return {
      isLoading,
      filters,
      searchKeyword,
      searchResults,
      filteredResults,
      updateFilter,
      goToProduct,
      handleAddToCart
    }
  }
}
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background-color: $bg-secondary;
  padding: $spacing-6 0;
  
  .search-header {
    margin-bottom: $spacing-6;
    
    .search-info {
      .search-title {
        font-size: $font-size-xxl;
        font-weight: $font-weight-bold;
        color: $text-primary;
        margin-bottom: $spacing-2;
        
        .keyword {
          color: $primary-color;
        }
      }
      
      .result-count {
        color: $text-secondary;
        font-size: $font-size-base;
      }
    }
  }
  
  .search-content {
    .search-filters {
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-4 $spacing-6;
      margin-bottom: $spacing-6;
      
      .filter-group {
        display: flex;
        align-items: center;
        gap: $spacing-4;
        margin-bottom: $spacing-4;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        @include respond-to(sm) {
          flex-direction: column;
          align-items: flex-start;
          gap: $spacing-2;
        }
        
        label {
          font-weight: $font-weight-semibold;
          color: $text-primary;
          min-width: 60px;
        }
        
        .filter-options {
          display: flex;
          gap: $spacing-2;
          flex-wrap: wrap;
          
          .filter-btn {
            padding: $spacing-2 $spacing-3;
            border: 1px solid $border-medium;
            border-radius: $border-radius-base;
            background-color: $bg-primary;
            color: $text-secondary;
            cursor: pointer;
            transition: all $transition-fast $ease-out;
            font-size: $font-size-sm;
            
            &:hover {
              border-color: $primary-color;
              color: $primary-color;
            }
            
            &.active {
              background-color: $primary-color;
              border-color: $primary-color;
              color: $text-white;
            }
          }
        }
      }
    }
    
    .search-results {
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-6;
    }
  }
}
</style>
