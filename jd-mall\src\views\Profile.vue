<template>
  <div class="profile-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">个人中心</h1>
      </div>

      <div class="profile-content">
        <div class="profile-sidebar">
          <div class="user-info">
            <img :src="userAvatar" :alt="userName" class="user-avatar" />
            <div class="user-details">
              <h3 class="user-name">{{ userName }}</h3>
              <div class="user-level">{{ userLevel }} 会员</div>
            </div>
          </div>

          <nav class="profile-nav">
            <a href="#" class="nav-item active">个人资料</a>
            <a href="#" class="nav-item">收货地址</a>
            <a href="#" class="nav-item">我的订单</a>
            <a href="#" class="nav-item">账户安全</a>
          </nav>
        </div>

        <div class="profile-main">
          <div class="profile-section">
            <h3 class="section-title">基本信息</h3>
            
            <form class="profile-form">
              <div class="form-group">
                <label>用户名</label>
                <input type="text" :value="user?.name" readonly />
              </div>
              
              <div class="form-group">
                <label>手机号</label>
                <input type="tel" :value="user?.phone" readonly />
              </div>
              
              <div class="form-group">
                <label>邮箱</label>
                <input type="email" :value="user?.email" />
              </div>
              
              <button type="submit" class="btn btn-primary">
                保存修改
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useUserStore } from '../stores/user.js'

export default {
  name: 'Profile',
  setup() {
    const userStore = useUserStore()

    const user = computed(() => userStore.user)
    const userName = computed(() => userStore.userName)
    const userAvatar = computed(() => userStore.userAvatar)
    const userLevel = computed(() => userStore.userLevel)

    return {
      user,
      userName,
      userAvatar,
      userLevel
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: $bg-secondary;
  padding: $spacing-6 0;
  
  .page-header {
    margin-bottom: $spacing-6;
    
    .page-title {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
    }
  }
  
  .profile-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: $spacing-6;
    
    @include respond-to(md) {
      grid-template-columns: 1fr;
    }
    
    .profile-sidebar {
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-6;
      height: fit-content;
      
      .user-info {
        display: flex;
        align-items: center;
        gap: $spacing-4;
        margin-bottom: $spacing-6;
        padding-bottom: $spacing-6;
        border-bottom: 1px solid $border-light;
        
        .user-avatar {
          width: 60px;
          height: 60px;
          border-radius: $border-radius-round;
          object-fit: cover;
        }
        
        .user-details {
          .user-name {
            font-size: $font-size-lg;
            font-weight: $font-weight-semibold;
            color: $text-primary;
            margin-bottom: $spacing-1;
          }
          
          .user-level {
            color: $primary-color;
            font-size: $font-size-sm;
          }
        }
      }
      
      .profile-nav {
        .nav-item {
          display: block;
          padding: $spacing-3 $spacing-4;
          color: $text-secondary;
          text-decoration: none;
          border-radius: $border-radius-base;
          margin-bottom: $spacing-1;
          transition: all $transition-fast $ease-out;
          
          &:hover {
            background-color: $bg-hover;
            color: $primary-color;
          }
          
          &.active {
            background-color: $primary-light;
            color: $primary-color;
          }
        }
      }
    }
    
    .profile-main {
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-6;
      
      .profile-section {
        .section-title {
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          color: $text-primary;
          margin-bottom: $spacing-6;
        }
        
        .profile-form {
          .form-group {
            margin-bottom: $spacing-5;
            
            label {
              display: block;
              font-weight: $font-weight-semibold;
              color: $text-primary;
              margin-bottom: $spacing-2;
            }
            
            input {
              width: 100%;
              max-width: 400px;
              padding: $spacing-3 $spacing-4;
              border: 1px solid $border-medium;
              border-radius: $border-radius-base;
              font-size: $font-size-base;
              
              &:focus {
                outline: none;
                border-color: $primary-color;
                box-shadow: $box-shadow-focus;
              }
              
              &[readonly] {
                background-color: $bg-disabled;
                color: $text-tertiary;
              }
            }
          }
        }
      }
    }
  }
}
</style>
