<template>
  <div class="login-page">
    <div class="container">
      <div class="login-container">
        <div class="login-header">
          <h1 class="login-title">登录</h1>
          <p class="login-subtitle">欢迎回来，请登录您的账户</p>
        </div>

        <form class="login-form" @submit.prevent="handleLogin">
          <div class="form-group">
            <label>用户名/手机号</label>
            <input 
              type="text" 
              v-model="loginForm.username"
              placeholder="请输入用户名或手机号"
              required
            />
          </div>

          <div class="form-group">
            <label>密码</label>
            <input 
              type="password" 
              v-model="loginForm.password"
              placeholder="请输入密码"
              required
            />
          </div>

          <div class="form-options">
            <label class="remember-me">
              <input type="checkbox" v-model="loginForm.remember" />
              <span>记住我</span>
            </label>
            <a href="#" class="forgot-password">忘记密码？</a>
          </div>

          <button 
            type="submit" 
            class="btn btn-primary login-btn"
            :disabled="isLoading"
          >
            {{ isLoading ? '登录中...' : '登录' }}
          </button>
        </form>

        <div class="login-footer">
          <p>还没有账户？<router-link to="/register">立即注册</router-link></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../stores/user.js'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const userStore = useUserStore()

    const isLoading = ref(false)
    const loginForm = ref({
      username: '',
      password: '',
      remember: false
    })

    const handleLogin = async () => {
      isLoading.value = true
      try {
        const result = await userStore.login(loginForm.value)
        if (result.success) {
          const redirectPath = route.query.redirect || '/'
          router.push(redirectPath)
        } else {
          alert('登录失败：' + result.error)
        }
      } catch (error) {
        console.error('登录失败:', error)
        alert('登录失败，请重试')
      } finally {
        isLoading.value = false
      }
    }

    return {
      isLoading,
      loginForm,
      handleLogin
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-light 0%, $secondary-light 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-6;
  
  .login-container {
    background-color: $bg-primary;
    border-radius: $border-radius-xl;
    padding: $spacing-8;
    box-shadow: $box-shadow-lg;
    width: 100%;
    max-width: 400px;
    
    .login-header {
      text-align: center;
      margin-bottom: $spacing-8;
      
      .login-title {
        font-size: $font-size-xxl;
        font-weight: $font-weight-bold;
        color: $text-primary;
        margin-bottom: $spacing-2;
      }
      
      .login-subtitle {
        color: $text-secondary;
        margin: 0;
      }
    }
    
    .login-form {
      .form-group {
        margin-bottom: $spacing-5;
        
        label {
          display: block;
          font-weight: $font-weight-semibold;
          color: $text-primary;
          margin-bottom: $spacing-2;
        }
        
        input {
          width: 100%;
          padding: $spacing-3 $spacing-4;
          border: 1px solid $border-medium;
          border-radius: $border-radius-base;
          font-size: $font-size-base;
          transition: border-color $transition-fast $ease-out;
          
          &:focus {
            outline: none;
            border-color: $primary-color;
            box-shadow: $box-shadow-focus;
          }
          
          &::placeholder {
            color: $text-placeholder;
          }
        }
      }
      
      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-6;
        
        .remember-me {
          display: flex;
          align-items: center;
          gap: $spacing-2;
          cursor: pointer;
          
          input[type="checkbox"] {
            width: auto;
          }
          
          span {
            color: $text-secondary;
            font-size: $font-size-sm;
          }
        }
        
        .forgot-password {
          color: $primary-color;
          text-decoration: none;
          font-size: $font-size-sm;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
      
      .login-btn {
        width: 100%;
        padding: $spacing-3 0;
        font-size: $font-size-base;
        font-weight: $font-weight-semibold;
        border-radius: $border-radius-base;
      }
    }
    
    .login-footer {
      text-align: center;
      margin-top: $spacing-6;
      
      p {
        color: $text-secondary;
        margin: 0;
        
        a {
          color: $primary-color;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>
