{"name": "micromatch", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "version": "4.0.8", "homepage": "https://github.com/micromatch/micromatch", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["(https://github.com/Diane<PERSON>ey)", "<PERSON><PERSON> (amilajack.com)", "<PERSON><PERSON><PERSON> (https://github.com/TrySound)", "<PERSON> (https://twitter.com/doowb)", "<PERSON> (http://badassjs.com)", "<PERSON><PERSON> (https://github.com/es128)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (https://ultcombo.js.org)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON> (https://kolarik.sk)", "<PERSON><PERSON><PERSON> (https://i.am.charlike.online)", "<PERSON> (paulmillr.com)", "<PERSON> (https://github.com/tomByrer)", "<PERSON> (http://rumkin.com)", "<PERSON> <<EMAIL>> (https://github.com/drpizza)", "<PERSON><PERSON> (https://github.com/ku8ar)"], "repository": "micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8.6"}, "scripts": {"test": "mocha"}, "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "devDependencies": {"fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "minimatch": "^5.0.1", "mocha": "^9.2.2", "time-require": "github:jonschlink<PERSON>/time-require"}, "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "verb": {"toc": "collapsible", "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}}