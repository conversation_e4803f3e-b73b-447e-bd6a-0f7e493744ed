<template>
  <div class="app-layout">
    <!-- 顶部通栏 -->
    <TopBar />
    
    <!-- 主导航栏 -->
    <Header />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <slot />
    </main>
    
    <!-- 页脚 -->
    <Footer />
  </div>
</template>

<script>
import TopBar from './TopBar.vue'
import Header from './Header.vue'
import Footer from './Footer.vue'

export default {
  name: 'Layout',
  components: {
    TopBar,
    Header,
    Footer
  }
}
</script>

<style lang="scss" scoped>
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  .main-content {
    flex: 1;
    padding: $spacing-lg 0;
    
    @include respond-to(xs) {
      padding: $spacing-base 0;
    }
  }
}
</style>
