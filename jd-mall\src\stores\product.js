import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { productData, categoryData } from '../data/mockData.js'

export const useProductStore = defineStore('product', () => {
  // 状态
  const products = ref([...productData])
  const categories = ref([...categoryData])
  const currentProduct = ref(null)
  const searchResults = ref([])
  const searchKeyword = ref('')
  const isLoading = ref(false)
  const filters = ref({
    category: '',
    priceRange: [0, 50000],
    brand: '',
    rating: 0,
    sortBy: 'default' // default, price-asc, price-desc, sales, rating
  })

  // 计算属性
  const filteredProducts = computed(() => {
    let result = [...products.value]

    // 分类筛选
    if (filters.value.category) {
      result = result.filter(product => 
        product.category === filters.value.category
      )
    }

    // 价格筛选
    result = result.filter(product => 
      product.price >= filters.value.priceRange[0] && 
      product.price <= filters.value.priceRange[1]
    )

    // 品牌筛选
    if (filters.value.brand) {
      result = result.filter(product => 
        product.brand === filters.value.brand
      )
    }

    // 评分筛选
    if (filters.value.rating > 0) {
      result = result.filter(product => 
        product.rating >= filters.value.rating
      )
    }

    // 排序
    switch (filters.value.sortBy) {
      case 'price-asc':
        result.sort((a, b) => a.price - b.price)
        break
      case 'price-desc':
        result.sort((a, b) => b.price - a.price)
        break
      case 'sales':
        result.sort((a, b) => (b.sales || 0) - (a.sales || 0))
        break
      case 'rating':
        result.sort((a, b) => b.rating - a.rating)
        break
      default:
        // 默认排序（可以是综合排序）
        break
    }

    return result
  })

  const hotProducts = computed(() => {
    return products.value
      .filter(product => product.tags?.includes('热销'))
      .slice(0, 8)
  })

  const newProducts = computed(() => {
    return products.value
      .filter(product => product.tags?.includes('新品'))
      .slice(0, 8)
  })

  const recommendProducts = computed(() => {
    // 简单的推荐算法：基于评分和销量
    return products.value
      .sort((a, b) => {
        const scoreA = (a.rating || 0) * 0.6 + (a.sales || 0) * 0.4 / 1000
        const scoreB = (b.rating || 0) * 0.6 + (b.sales || 0) * 0.4 / 1000
        return scoreB - scoreA
      })
      .slice(0, 12)
  })

  // 动作
  const fetchProducts = async (params = {}) => {
    isLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // 这里可以根据params获取不同的商品数据
      // 目前使用静态数据
      
      return { success: true, data: products.value }
    } catch (error) {
      console.error('获取商品列表失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const fetchProductDetail = async (productId) => {
    isLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 600))
      
      const product = products.value.find(p => p.id == productId)
      if (product) {
        // 增强商品详情数据
        currentProduct.value = {
          ...product,
          description: `${product.title}的详细描述信息。这是一款优质的商品，具有出色的性能和品质保证。`,
          specifications: [
            { name: '品牌', value: product.shop?.replace('官方旗舰店', '') || '未知' },
            { name: '型号', value: `Model-${product.id}` },
            { name: '颜色', value: '多色可选' },
            { name: '尺寸', value: '标准尺寸' },
            { name: '重量', value: '适中' },
            { name: '保修', value: '1年质保' }
          ],
          images: [
            product.image,
            product.image.replace('280', '400'),
            product.image.replace('280', '600'),
            product.image.replace('280', '800')
          ],
          specs: [
            {
              name: '颜色',
              options: ['黑色', '白色', '蓝色', '红色']
            },
            {
              name: '容量',
              options: ['64GB', '128GB', '256GB', '512GB']
            }
          ],
          reviews: generateMockReviews(product.reviewCount || 100),
          relatedProducts: products.value
            .filter(p => p.id !== product.id)
            .slice(0, 6)
        }
        
        return { success: true, data: currentProduct.value }
      } else {
        return { success: false, error: '商品不存在' }
      }
    } catch (error) {
      console.error('获取商品详情失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const searchProducts = async (keyword, options = {}) => {
    isLoading.value = true
    searchKeyword.value = keyword
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (!keyword.trim()) {
        searchResults.value = []
        return { success: true, data: [] }
      }
      
      // 简单的搜索算法
      const results = products.value.filter(product => 
        product.title.toLowerCase().includes(keyword.toLowerCase()) ||
        product.shop?.toLowerCase().includes(keyword.toLowerCase()) ||
        product.tags?.some(tag => tag.includes(keyword))
      )
      
      searchResults.value = results
      return { success: true, data: results }
    } catch (error) {
      console.error('搜索商品失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const updateFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const resetFilters = () => {
    filters.value = {
      category: '',
      priceRange: [0, 50000],
      brand: '',
      rating: 0,
      sortBy: 'default'
    }
  }

  const getProductById = (productId) => {
    return products.value.find(p => p.id == productId)
  }

  const getCategoryById = (categoryId) => {
    return categories.value.find(c => c.id == categoryId)
  }

  const getProductsByCategory = (categoryId) => {
    return products.value.filter(p => p.categoryId == categoryId)
  }

  // 获取搜索建议
  const getSearchSuggestions = (keyword) => {
    if (!keyword.trim()) return []
    
    const suggestions = []
    const lowerKeyword = keyword.toLowerCase()
    
    // 从商品标题中提取建议
    products.value.forEach(product => {
      const title = product.title.toLowerCase()
      if (title.includes(lowerKeyword)) {
        suggestions.push(product.title)
      }
    })
    
    // 从品牌中提取建议
    const brands = [...new Set(products.value.map(p => p.shop?.replace('官方旗舰店', '')).filter(Boolean))]
    brands.forEach(brand => {
      if (brand.toLowerCase().includes(lowerKeyword)) {
        suggestions.push(brand)
      }
    })
    
    return [...new Set(suggestions)].slice(0, 8)
  }

  return {
    // 状态
    products,
    categories,
    currentProduct,
    searchResults,
    searchKeyword,
    isLoading,
    filters,
    
    // 计算属性
    filteredProducts,
    hotProducts,
    newProducts,
    recommendProducts,
    
    // 动作
    fetchProducts,
    fetchProductDetail,
    searchProducts,
    updateFilters,
    resetFilters,
    getProductById,
    getCategoryById,
    getProductsByCategory,
    getSearchSuggestions
  }
})

// 生成模拟评价数据
function generateMockReviews(count) {
  const reviews = []
  const userNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  const reviewTexts = [
    '商品质量很好，物流很快，包装完整，非常满意！',
    '性价比很高，功能齐全，使用体验不错。',
    '外观漂亮，做工精细，值得推荐。',
    '发货速度快，商品和描述一致，好评！',
    '用了一段时间，质量稳定，没有问题。',
    '客服态度很好，解答问题很耐心。',
    '包装很用心，商品保护得很好。',
    '功能强大，操作简单，很实用。'
  ]
  
  for (let i = 0; i < Math.min(count, 50); i++) {
    reviews.push({
      id: i + 1,
      userName: userNames[Math.floor(Math.random() * userNames.length)],
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${i}`,
      rating: Math.floor(Math.random() * 2) + 4, // 4-5星
      content: reviewTexts[Math.floor(Math.random() * reviewTexts.length)],
      images: Math.random() > 0.7 ? [`https://picsum.photos/200/200?random=${i}`] : [],
      date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      helpful: Math.floor(Math.random() * 20)
    })
  }
  
  return reviews.sort((a, b) => new Date(b.date) - new Date(a.date))
}
