(function(l,o){"use strict";function $(t,e){var n;const r=o.shallowRef();return o.watchEffect(()=>{r.value=t()},{...e,flush:(n=e?.flush)!=null?n:"sync"}),o.readonly(r)}function H(t,e,n={}){let r,a,i,c=!0;const u=()=>{c=!0,i()};o.watch(t,u,{flush:"sync",...n});const s=typeof e=="function"?e:e.get,h=typeof e=="function"?void 0:e.set,d=o.customRef((m,f)=>(a=m,i=f,{get(){return c&&(r=s(r),c=!1),a(),r},set(y){h?.(y)}}));return d.trigger=u,d}function p(t){return o.getCurrentScope()?(o.onScopeDispose(t),!0):!1}function dt(){const t=new Set,e=i=>{t.delete(i)};return{on:i=>{t.add(i);const c=()=>e(i);return p(c),{off:c}},off:e,trigger:(...i)=>Promise.all(Array.from(t).map(c=>c(...i))),clear:()=>{t.clear()}}}function mt(t){let e=!1,n;const r=o.effectScope(!0);return(...a)=>(e||(n=r.run(()=>t(...a)),e=!0),n)}const O=new WeakMap,G=(...t)=>{var e;const n=t[0],r=(e=o.getCurrentInstance())==null?void 0:e.proxy;if(r==null&&!o.hasInjectionContext())throw new Error("injectLocal must be called in setup");return r&&O.has(r)&&n in O.get(r)?O.get(r)[n]:o.inject(...t)};function Z(t,e){var n;const r=(n=o.getCurrentInstance())==null?void 0:n.proxy;if(r==null)throw new Error("provideLocal must be called in setup");O.has(r)||O.set(r,Object.create(null));const a=O.get(r);return a[t]=e,o.provide(t,e)}function ht(t,e){const n=e?.injectionKey||Symbol(t.name||"InjectionState"),r=e?.defaultValue;return[(...c)=>{const u=t(...c);return Z(n,u),u},()=>G(n,r)]}function yt(t,e){return e===!0?o.ref(t):o.shallowRef(t)}function wt(t){let e=0,n,r;const a=()=>{e-=1,r&&e<=0&&(r.stop(),n=void 0,r=void 0)};return(...i)=>(e+=1,r||(r=o.effectScope(!0),n=r.run(()=>t(...i))),p(a),n)}function q(t,e,{enumerable:n=!1,unwrap:r=!0}={}){for(const[a,i]of Object.entries(e))a!=="value"&&(o.isRef(i)&&r?Object.defineProperty(t,a,{get(){return i.value},set(c){i.value=c},enumerable:n}):Object.defineProperty(t,a,{value:i,enumerable:n}));return t}function gt(t,e){return e==null?o.unref(t):o.unref(t)[e]}function Vt(t){return o.unref(t)!=null}function bt(t,e){if(typeof Symbol<"u"){const n={...t};return Object.defineProperty(n,Symbol.iterator,{enumerable:!1,value(){let r=0;return{next:()=>({value:e[r++],done:r>e.length})}}}),n}else return Object.assign([...e],t)}function _(t,e){const n=e?.computedGetter===!1?o.unref:o.toValue;return function(...r){return o.computed(()=>t.apply(this,r.map(a=>n(a))))}}function pt(t,e={}){let n=[],r;if(Array.isArray(e))n=e;else{r=e;const{includeOwnProperties:a=!0}=e;n.push(...Object.keys(t)),a&&n.push(...Object.getOwnPropertyNames(t))}return Object.fromEntries(n.map(a=>{const i=t[a];return[a,typeof i=="function"?_(i.bind(t),r):i]}))}function J(t){if(!o.isRef(t))return o.reactive(t);const e=new Proxy({},{get(n,r,a){return o.unref(Reflect.get(t.value,r,a))},set(n,r,a){return o.isRef(t.value[r])&&!o.isRef(a)?t.value[r].value=a:t.value[r]=a,!0},deleteProperty(n,r){return Reflect.deleteProperty(t.value,r)},has(n,r){return Reflect.has(t.value,r)},ownKeys(){return Object.keys(t.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return o.reactive(e)}function N(t){return J(o.computed(t))}function At(t,...e){const n=e.flat(),r=n[0];return N(()=>Object.fromEntries(typeof r=="function"?Object.entries(o.toRefs(t)).filter(([a,i])=>!r(o.toValue(i),a)):Object.entries(o.toRefs(t)).filter(a=>!n.includes(a[0]))))}const D=typeof window<"u"&&typeof document<"u",St=typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope,Ot=t=>typeof t<"u",Rt=t=>t!=null,Tt=(t,...e)=>{t||console.warn(...e)},Dt=Object.prototype.toString,X=t=>Dt.call(t)==="[object Object]",Ft=()=>Date.now(),K=()=>+Date.now(),Mt=(t,e,n)=>Math.min(n,Math.max(e,t)),A=()=>{},Pt=(t,e)=>(t=Math.ceil(t),e=Math.floor(e),Math.floor(Math.random()*(e-t+1))+t),It=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),Ct=kt();function kt(){var t,e;return D&&((t=window?.navigator)==null?void 0:t.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((e=window?.navigator)==null?void 0:e.maxTouchPoints)>2&&/iPad|Macintosh/.test(window?.navigator.userAgent))}function I(...t){if(t.length!==1)return o.toRef(...t);const e=t[0];return typeof e=="function"?o.readonly(o.customRef(()=>({get:e,set:A}))):o.ref(e)}const Et=I;function _t(t,...e){const n=e.flat(),r=n[0];return N(()=>Object.fromEntries(typeof r=="function"?Object.entries(o.toRefs(t)).filter(([a,i])=>r(o.toValue(i),a)):n.map(a=>[a,I(t,a)])))}function Q(t,e=1e4){return o.customRef((n,r)=>{let a=o.toValue(t),i;const c=()=>setTimeout(()=>{a=o.toValue(t),r()},o.toValue(e));return p(()=>{clearTimeout(i)}),{get(){return n(),a},set(u){a=u,r(),clearTimeout(i),i=c()}}})}function F(t,e){function n(...r){return new Promise((a,i)=>{Promise.resolve(t(()=>e.apply(this,r),{fn:e,thisArg:this,args:r})).then(a).catch(i)})}return n}const C=t=>t();function L(t,e={}){let n,r,a=A;const i=s=>{clearTimeout(s),a(),a=A};let c;return s=>{const h=o.toValue(t),d=o.toValue(e.maxWait);return n&&i(n),h<=0||d!==void 0&&d<=0?(r&&(i(r),r=void 0),Promise.resolve(s())):new Promise((m,f)=>{a=e.rejectOnCancel?f:m,c=s,d&&!r&&(r=setTimeout(()=>{n&&i(n),r=void 0,m(c())},d)),n=setTimeout(()=>{r&&i(r),r=void 0,m(s())},h)})}}function j(...t){let e=0,n,r=!0,a=A,i,c,u,s,h;!o.isRef(t[0])&&typeof t[0]=="object"?{delay:c,trailing:u=!0,leading:s=!0,rejectOnCancel:h=!1}=t[0]:[c,u=!0,s=!0,h=!1]=t;const d=()=>{n&&(clearTimeout(n),n=void 0,a(),a=A)};return f=>{const y=o.toValue(c),g=Date.now()-e,V=()=>i=f();return d(),y<=0?(e=Date.now(),V()):(g>y&&(s||!r)?(e=Date.now(),V()):u&&(i=new Promise((w,b)=>{a=h?b:w,n=setTimeout(()=>{e=Date.now(),r=!0,w(V()),d()},Math.max(0,y-g))})),!s&&!n&&(n=setTimeout(()=>r=!0,y)),r=!1,i)}}function v(t=C,e={}){const{initialState:n="active"}=e,r=I(n==="active");function a(){r.value=!1}function i(){r.value=!0}const c=(...u)=>{r.value&&t(...u)};return{isActive:o.readonly(r),pause:a,resume:i,eventFilter:c}}function W(t,e=!1,n="Timeout"){return new Promise((r,a)=>{setTimeout(e?()=>a(n):r,t)})}function Nt(t){return t}function Lt(t){let e;function n(){return e||(e=t()),e}return n.reset=async()=>{const r=e;e=void 0,r&&await r},n}function jt(t){return t()}function x(t,...e){return e.some(n=>n in t)}function Wt(t,e){var n;if(typeof t=="number")return t+e;const r=((n=t.match(/^-?\d+\.?\d*/))==null?void 0:n[0])||"",a=t.slice(r.length),i=Number.parseFloat(r)+e;return Number.isNaN(i)?t:i+a}function Ut(t){return t.endsWith("rem")?Number.parseFloat(t)*16:Number.parseFloat(t)}function zt(t,e,n=!1){return e.reduce((r,a)=>(a in t&&(!n||t[a]!==void 0)&&(r[a]=t[a]),r),{})}function Bt(t,e,n=!1){return Object.fromEntries(Object.entries(t).filter(([r,a])=>(!n||a!==void 0)&&!e.includes(r)))}function Yt(t){return Object.entries(t)}function tt(t){return Array.isArray(t)?t:[t]}function et(t){const e=Object.create(null);return n=>e[n]||(e[n]=t(n))}const $t=/\B([A-Z])/g,Ht=et(t=>t.replace($t,"-$1").toLowerCase()),Gt=/-(\w)/g,Zt=et(t=>t.replace(Gt,(e,n)=>n?n.toUpperCase():""));function M(t){return t||o.getCurrentInstance()}function nt(t,e=200,n={}){return F(L(e,n),t)}function U(t,e=200,n={}){const r=o.ref(o.toValue(t)),a=nt(()=>{r.value=t.value},e,n);return o.watch(t,()=>a()),o.shallowReadonly(r)}function qt(t,e){return o.computed({get(){var n;return(n=t.value)!=null?n:e},set(n){t.value=n}})}function rt(t,e=200,n=!1,r=!0,a=!1){return F(j(e,n,r,a),t)}function z(t,e=200,n=!0,r=!0){if(e<=0)return t;const a=o.ref(o.toValue(t)),i=rt(()=>{a.value=t.value},e,n,r);return o.watch(t,()=>i()),a}function ot(t,e={}){let n=t,r,a;const i=o.customRef((f,y)=>(r=f,a=y,{get(){return c()},set(g){u(g)}}));function c(f=!0){return f&&r(),n}function u(f,y=!0){var g,V;if(f===n)return;const w=n;((g=e.onBeforeChange)==null?void 0:g.call(e,f,w))!==!1&&(n=f,(V=e.onChanged)==null||V.call(e,f,w),y&&a())}return q(i,{get:c,set:u,untrackedGet:()=>c(!1),silentSet:f=>u(f,!1),peek:()=>c(!1),lay:f=>u(f,!1)},{enumerable:!0})}const Jt=ot;function Xt(...t){if(t.length===2){const[e,n]=t;e.value=n}if(t.length===3){const[e,n,r]=t;e[n]=r}}function P(t,e,n={}){const{eventFilter:r=C,...a}=n;return o.watch(t,F(r,e),a)}function k(t,e,n={}){const{eventFilter:r,initialState:a="active",...i}=n,{eventFilter:c,pause:u,resume:s,isActive:h}=v(r,{initialState:a});return{stop:P(t,e,{...i,eventFilter:c}),pause:u,resume:s,isActive:h}}function Kt(t,e,...[n]){const{flush:r="sync",deep:a=!1,immediate:i=!0,direction:c="both",transform:u={}}=n||{},s=[],h="ltr"in u&&u.ltr||(f=>f),d="rtl"in u&&u.rtl||(f=>f);return(c==="both"||c==="ltr")&&s.push(k(t,f=>{s.forEach(y=>y.pause()),e.value=h(f),s.forEach(y=>y.resume())},{flush:r,deep:a,immediate:i})),(c==="both"||c==="rtl")&&s.push(k(e,f=>{s.forEach(y=>y.pause()),t.value=d(f),s.forEach(y=>y.resume())},{flush:r,deep:a,immediate:i})),()=>{s.forEach(f=>f.stop())}}function Qt(t,e,n={}){const{flush:r="sync",deep:a=!1,immediate:i=!0}=n,c=tt(e);return o.watch(t,u=>c.forEach(s=>s.value=u),{flush:r,deep:a,immediate:i})}function vt(t,e={}){if(!o.isRef(t))return o.toRefs(t);const n=Array.isArray(t.value)?Array.from({length:t.value.length}):{};for(const r in t.value)n[r]=o.customRef(()=>({get(){return t.value[r]},set(a){var i;if((i=o.toValue(e.replaceRef))!=null?i:!0)if(Array.isArray(t.value)){const u=[...t.value];u[r]=a,t.value=u}else{const u={...t.value,[r]:a};Object.setPrototypeOf(u,Object.getPrototypeOf(t.value)),t.value=u}else t.value[r]=a}}));return n}const xt=o.toValue,te=o.toValue;function ee(t,e=!0,n){M(n)?o.onBeforeMount(t,n):e?t():o.nextTick(t)}function ne(t,e){M(e)&&o.onBeforeUnmount(t,e)}function re(t,e=!0,n){M(n)?o.onMounted(t,n):e?t():o.nextTick(t)}function oe(t,e){M(e)&&o.onUnmounted(t,e)}function B(t,e=!1){function n(m,{flush:f="sync",deep:y=!1,timeout:g,throwOnTimeout:V}={}){let w=null;const R=[new Promise(E=>{w=o.watch(t,T=>{m(T)!==e&&(w?w():o.nextTick(()=>w?.()),E(T))},{flush:f,deep:y,immediate:!0})})];return g!=null&&R.push(W(g,V).then(()=>o.toValue(t)).finally(()=>w?.())),Promise.race(R)}function r(m,f){if(!o.isRef(m))return n(T=>T===m,f);const{flush:y="sync",deep:g=!1,timeout:V,throwOnTimeout:w}=f??{};let b=null;const E=[new Promise(T=>{b=o.watch([t,m],([ft,$e])=>{e!==(ft===$e)&&(b?b():o.nextTick(()=>b?.()),T(ft))},{flush:y,deep:g,immediate:!0})})];return V!=null&&E.push(W(V,w).then(()=>o.toValue(t)).finally(()=>(b?.(),o.toValue(t)))),Promise.race(E)}function a(m){return n(f=>!!f,m)}function i(m){return r(null,m)}function c(m){return r(void 0,m)}function u(m){return n(Number.isNaN,m)}function s(m,f){return n(y=>{const g=Array.from(y);return g.includes(m)||g.includes(o.toValue(m))},f)}function h(m){return d(1,m)}function d(m=1,f){let y=-1;return n(()=>(y+=1,y>=m),f)}return Array.isArray(o.toValue(t))?{toMatch:n,toContains:s,changed:h,changedTimes:d,get not(){return B(t,!e)}}:{toMatch:n,toBe:r,toBeTruthy:a,toBeNull:i,toBeNaN:u,toBeUndefined:c,changed:h,changedTimes:d,get not(){return B(t,!e)}}}function ae(t){return B(t)}function ie(t,e){return t===e}function le(...t){var e,n;const r=t[0],a=t[1];let i=(e=t[2])!=null?e:ie;const{symmetric:c=!1}=(n=t[3])!=null?n:{};if(typeof i=="string"){const s=i;i=(h,d)=>h[s]===d[s]}const u=o.computed(()=>o.toValue(r).filter(s=>o.toValue(a).findIndex(h=>i(s,h))===-1));if(c){const s=o.computed(()=>o.toValue(a).filter(h=>o.toValue(r).findIndex(d=>i(h,d))===-1));return o.computed(()=>c?[...o.toValue(u),...o.toValue(s)]:o.toValue(u))}else return u}function ce(t,e){return o.computed(()=>o.toValue(t).every((n,r,a)=>e(o.toValue(n),r,a)))}function ue(t,e){return o.computed(()=>o.toValue(t).map(n=>o.toValue(n)).filter(e))}function se(t,e){return o.computed(()=>o.toValue(o.toValue(t).find((n,r,a)=>e(o.toValue(n),r,a))))}function fe(t,e){return o.computed(()=>o.toValue(t).findIndex((n,r,a)=>e(o.toValue(n),r,a)))}function de(t,e){let n=t.length;for(;n-- >0;)if(e(t[n],n,t))return t[n]}function me(t,e){return o.computed(()=>o.toValue(Array.prototype.findLast?o.toValue(t).findLast((n,r,a)=>e(o.toValue(n),r,a)):de(o.toValue(t),(n,r,a)=>e(o.toValue(n),r,a))))}function he(t){return X(t)&&x(t,"formIndex","comparator")}function ye(...t){var e;const n=t[0],r=t[1];let a=t[2],i=0;if(he(a)&&(i=(e=a.fromIndex)!=null?e:0,a=a.comparator),typeof a=="string"){const c=a;a=(u,s)=>u[c]===o.toValue(s)}return a=a??((c,u)=>c===o.toValue(u)),o.computed(()=>o.toValue(n).slice(i).some((c,u,s)=>a(o.toValue(c),o.toValue(r),u,o.toValue(s))))}function we(t,e){return o.computed(()=>o.toValue(t).map(n=>o.toValue(n)).join(o.toValue(e)))}function ge(t,e){return o.computed(()=>o.toValue(t).map(n=>o.toValue(n)).map(e))}function Ve(t,e,...n){const r=(a,i,c)=>e(o.toValue(a),o.toValue(i),c);return o.computed(()=>{const a=o.toValue(t);return n.length?a.reduce(r,typeof n[0]=="function"?o.toValue(n[0]()):o.toValue(n[0])):a.reduce(r)})}function be(t,e){return o.computed(()=>o.toValue(t).some((n,r,a)=>e(o.toValue(n),r,a)))}function pe(t){return Array.from(new Set(t))}function Ae(t,e){return t.reduce((n,r)=>(n.some(a=>e(r,a,t))||n.push(r),n),[])}function Se(t,e){return o.computed(()=>{const n=o.toValue(t).map(r=>o.toValue(r));return e?Ae(n,e):pe(n)})}function Oe(t=0,e={}){let n=o.unref(t);const r=o.shallowRef(t),{max:a=Number.POSITIVE_INFINITY,min:i=Number.NEGATIVE_INFINITY}=e,c=(m=1)=>r.value=Math.max(Math.min(a,r.value+m),i),u=(m=1)=>r.value=Math.min(Math.max(i,r.value-m),a),s=()=>r.value,h=m=>r.value=Math.max(i,Math.min(a,m)),d=(m=n)=>(n=m,h(m));return{count:o.shallowReadonly(r),inc:c,dec:u,get:s,set:h,reset:d}}const Re=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[T\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/i,Te=/[YMDHhms]o|\[([^\]]+)\]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|z{1,4}|SSS/g;function De(t,e,n,r){let a=t<12?"AM":"PM";return r&&(a=a.split("").reduce((i,c)=>i+=`${c}.`,"")),n?a.toLowerCase():a}function S(t){const e=["th","st","nd","rd"],n=t%100;return t+(e[(n-20)%10]||e[n]||e[0])}function at(t,e,n={}){var r;const a=t.getFullYear(),i=t.getMonth(),c=t.getDate(),u=t.getHours(),s=t.getMinutes(),h=t.getSeconds(),d=t.getMilliseconds(),m=t.getDay(),f=(r=n.customMeridiem)!=null?r:De,y=V=>{var w;return(w=V.split(" ")[1])!=null?w:""},g={Yo:()=>S(a),YY:()=>String(a).slice(-2),YYYY:()=>a,M:()=>i+1,Mo:()=>S(i+1),MM:()=>`${i+1}`.padStart(2,"0"),MMM:()=>t.toLocaleDateString(o.toValue(n.locales),{month:"short"}),MMMM:()=>t.toLocaleDateString(o.toValue(n.locales),{month:"long"}),D:()=>String(c),Do:()=>S(c),DD:()=>`${c}`.padStart(2,"0"),H:()=>String(u),Ho:()=>S(u),HH:()=>`${u}`.padStart(2,"0"),h:()=>`${u%12||12}`.padStart(1,"0"),ho:()=>S(u%12||12),hh:()=>`${u%12||12}`.padStart(2,"0"),m:()=>String(s),mo:()=>S(s),mm:()=>`${s}`.padStart(2,"0"),s:()=>String(h),so:()=>S(h),ss:()=>`${h}`.padStart(2,"0"),SSS:()=>`${d}`.padStart(3,"0"),d:()=>m,dd:()=>t.toLocaleDateString(o.toValue(n.locales),{weekday:"narrow"}),ddd:()=>t.toLocaleDateString(o.toValue(n.locales),{weekday:"short"}),dddd:()=>t.toLocaleDateString(o.toValue(n.locales),{weekday:"long"}),A:()=>f(u,s),AA:()=>f(u,s,!1,!0),a:()=>f(u,s,!0),aa:()=>f(u,s,!0,!0),z:()=>y(t.toLocaleDateString(o.toValue(n.locales),{timeZoneName:"shortOffset"})),zz:()=>y(t.toLocaleDateString(o.toValue(n.locales),{timeZoneName:"shortOffset"})),zzz:()=>y(t.toLocaleDateString(o.toValue(n.locales),{timeZoneName:"shortOffset"})),zzzz:()=>y(t.toLocaleDateString(o.toValue(n.locales),{timeZoneName:"longOffset"}))};return e.replace(Te,(V,w)=>{var b,R;return(R=w??((b=g[V])==null?void 0:b.call(g)))!=null?R:V})}function it(t){if(t===null)return new Date(Number.NaN);if(t===void 0)return new Date;if(t instanceof Date)return new Date(t);if(typeof t=="string"&&!/Z$/i.test(t)){const e=t.match(Re);if(e){const n=e[2]-1||0,r=(e[7]||"0").substring(0,3);return new Date(e[1],n,e[3]||1,e[4]||0,e[5]||0,e[6]||0,r)}}return new Date(t)}function Fe(t,e="HH:mm:ss",n={}){return o.computed(()=>at(it(o.toValue(t)),o.toValue(e),n))}function lt(t,e=1e3,n={}){const{immediate:r=!0,immediateCallback:a=!1}=n;let i=null;const c=o.shallowRef(!1);function u(){i&&(clearInterval(i),i=null)}function s(){c.value=!1,u()}function h(){const d=o.toValue(e);d<=0||(c.value=!0,a&&t(),u(),c.value&&(i=setInterval(t,d)))}if(r&&D&&h(),o.isRef(e)||typeof e=="function"){const d=o.watch(e,()=>{c.value&&D&&h()});p(d)}return p(s),{isActive:o.shallowReadonly(c),pause:s,resume:h}}function Me(t=1e3,e={}){const{controls:n=!1,immediate:r=!0,callback:a}=e,i=o.shallowRef(0),c=()=>i.value+=1,u=()=>{i.value=0},s=lt(a?()=>{c(),a(i.value)}:c,t,{immediate:r});return n?{counter:o.shallowReadonly(i),reset:u,...s}:o.shallowReadonly(i)}function Pe(t,e={}){var n;const r=o.shallowRef((n=e.initialValue)!=null?n:null);return o.watch(t,()=>r.value=K(),e),o.shallowReadonly(r)}function ct(t,e,n={}){const{immediate:r=!0,immediateCallback:a=!1}=n,i=o.shallowRef(!1);let c;function u(){c&&(clearTimeout(c),c=void 0)}function s(){i.value=!1,u()}function h(...d){a&&t(),u(),i.value=!0,c=setTimeout(()=>{i.value=!1,c=void 0,t(...d)},o.toValue(e))}return r&&(i.value=!0,D&&h()),p(s),{isPending:o.shallowReadonly(i),start:h,stop:s}}function Ie(t=1e3,e={}){const{controls:n=!1,callback:r}=e,a=ct(r??A,t,e),i=o.computed(()=>!a.isPending.value);return n?{ready:i,...a}:i}function Ce(t,e={}){const{method:n="parseFloat",radix:r,nanToZero:a}=e;return o.computed(()=>{let i=o.toValue(t);return typeof n=="function"?i=n(i):typeof i=="string"&&(i=Number[n](i,r)),a&&Number.isNaN(i)&&(i=0),i})}function ke(t){return o.computed(()=>`${o.toValue(t)}`)}function Ee(t=!1,e={}){const{truthyValue:n=!0,falsyValue:r=!1}=e,a=o.isRef(t),i=o.shallowRef(t);function c(u){if(arguments.length)return i.value=u,i.value;{const s=o.toValue(n);return i.value=i.value===s?o.toValue(r):s,i.value}}return a?c:[i,c]}function _e(t,e,n){let r=n?.immediate?[]:[...typeof t=="function"?t():Array.isArray(t)?t:o.toValue(t)];return o.watch(t,(a,i,c)=>{const u=Array.from({length:r.length}),s=[];for(const d of a){let m=!1;for(let f=0;f<r.length;f++)if(!u[f]&&d===r[f]){u[f]=!0,m=!0;break}m||s.push(d)}const h=r.filter((d,m)=>!u[m]);e(a,r,s,h,c),r=[...a]},n)}function Ne(t,e,n){const{count:r,...a}=n,i=o.shallowRef(0),c=P(t,(...u)=>{i.value+=1,i.value>=o.toValue(r)&&o.nextTick(()=>c()),e(...u)},a);return{count:i,stop:c}}function ut(t,e,n={}){const{debounce:r=0,maxWait:a=void 0,...i}=n;return P(t,e,{...i,eventFilter:L(r,{maxWait:a})})}function Le(t,e,n){return o.watch(t,e,{...n,deep:!0})}function Y(t,e,n={}){const{eventFilter:r=C,...a}=n,i=F(r,e);let c,u,s;if(a.flush==="sync"){let h=!1;u=()=>{},c=d=>{h=!0,d(),h=!1},s=o.watch(t,(...d)=>{h||i(...d)},a)}else{const h=[];let d=0,m=0;u=()=>{d=m},h.push(o.watch(t,()=>{m++},{...a,flush:"sync"})),c=f=>{const y=m;f(),d+=m-y},h.push(o.watch(t,(...f)=>{const y=d>0&&d===m;d=0,m=0,!y&&i(...f)},a)),s=()=>{h.forEach(f=>f())}}return{stop:s,ignoreUpdates:c,ignorePrevAsyncUpdates:u}}function je(t,e,n){return o.watch(t,e,{...n,immediate:!0})}function We(t,e,n){return o.watch(t,e,{...n,once:!0})}function st(t,e,n={}){const{throttle:r=0,trailing:a=!0,leading:i=!0,...c}=n;return P(t,e,{...c,eventFilter:j(r,a,i)})}function Ue(t,e,n={}){let r;function a(){if(!r)return;const d=r;r=void 0,d()}function i(d){r=d}const c=(d,m)=>(a(),e(d,m,i)),u=Y(t,c,n),{ignoreUpdates:s}=u;return{...u,trigger:()=>{let d;return s(()=>{d=c(ze(t),Be(t))}),d}}}function ze(t){return o.isReactive(t)?t:Array.isArray(t)?t.map(e=>o.toValue(e)):o.toValue(t)}function Be(t){return Array.isArray(t)?t.map(()=>{}):void 0}function Ye(t,e,n){const r=o.watch(t,(a,i,c)=>{a&&(n?.once&&o.nextTick(()=>r()),e(a,i,c))},{...n,once:!1});return r}l.assert=Tt,l.autoResetRef=Q,l.bypassFilter=C,l.camelize=Zt,l.clamp=Mt,l.computedEager=$,l.computedWithControl=H,l.containsProp=x,l.controlledComputed=H,l.controlledRef=Jt,l.createEventHook=dt,l.createFilterWrapper=F,l.createGlobalState=mt,l.createInjectionState=ht,l.createReactiveFn=_,l.createRef=yt,l.createSharedComposable=wt,l.createSingletonPromise=Lt,l.debounceFilter=L,l.debouncedRef=U,l.debouncedWatch=ut,l.eagerComputed=$,l.extendRef=q,l.formatDate=at,l.get=gt,l.getLifeCycleTarget=M,l.hasOwn=It,l.hyphenate=Ht,l.identity=Nt,l.ignorableWatch=Y,l.increaseWithUnit=Wt,l.injectLocal=G,l.invoke=jt,l.isClient=D,l.isDef=Ot,l.isDefined=Vt,l.isIOS=Ct,l.isObject=X,l.isWorker=St,l.makeDestructurable=bt,l.noop=A,l.normalizeDate=it,l.notNullish=Rt,l.now=Ft,l.objectEntries=Yt,l.objectOmit=Bt,l.objectPick=zt,l.pausableFilter=v,l.pausableWatch=k,l.promiseTimeout=W,l.provideLocal=Z,l.pxValue=Ut,l.rand=Pt,l.reactify=_,l.reactifyObject=pt,l.reactiveComputed=N,l.reactiveOmit=At,l.reactivePick=_t,l.refAutoReset=Q,l.refDebounced=U,l.refDefault=qt,l.refThrottled=z,l.refWithControl=ot,l.resolveRef=Et,l.resolveUnref=te,l.set=Xt,l.syncRef=Kt,l.syncRefs=Qt,l.throttleFilter=j,l.throttledRef=z,l.throttledWatch=st,l.timestamp=K,l.toArray=tt,l.toReactive=J,l.toRef=I,l.toRefs=vt,l.toValue=xt,l.tryOnBeforeMount=ee,l.tryOnBeforeUnmount=ne,l.tryOnMounted=re,l.tryOnScopeDispose=p,l.tryOnUnmounted=oe,l.until=ae,l.useArrayDifference=le,l.useArrayEvery=ce,l.useArrayFilter=ue,l.useArrayFind=se,l.useArrayFindIndex=fe,l.useArrayFindLast=me,l.useArrayIncludes=ye,l.useArrayJoin=we,l.useArrayMap=ge,l.useArrayReduce=Ve,l.useArraySome=be,l.useArrayUnique=Se,l.useCounter=Oe,l.useDateFormat=Fe,l.useDebounce=U,l.useDebounceFn=nt,l.useInterval=Me,l.useIntervalFn=lt,l.useLastChanged=Pe,l.useThrottle=z,l.useThrottleFn=rt,l.useTimeout=Ie,l.useTimeoutFn=ct,l.useToNumber=Ce,l.useToString=ke,l.useToggle=Ee,l.watchArray=_e,l.watchAtMost=Ne,l.watchDebounced=ut,l.watchDeep=Le,l.watchIgnorable=Y,l.watchImmediate=je,l.watchOnce=We,l.watchPausable=k,l.watchThrottled=st,l.watchTriggerable=Ue,l.watchWithFilter=P,l.whenever=Ye})(this.VueUse=this.VueUse||{},Vue);
