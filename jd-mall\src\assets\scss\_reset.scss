// 京东商城样式重置
// ==========================================

// 基础重置
// ------------------------------------------
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: $font-family-base;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: $text-primary;
  background-color: $bg-secondary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 标题重置
// ------------------------------------------
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: $font-weight-semibold;
  line-height: 1.2;
}

h1 { font-size: $font-size-xxl; }
h2 { font-size: $font-size-xl; }
h3 { font-size: $font-size-lg; }
h4 { font-size: $font-size-base; }
h5 { font-size: $font-size-sm; }
h6 { font-size: $font-size-xs; }

// 段落和文本
// ------------------------------------------
p {
  margin: 0 0 $spacing-base 0;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 链接
// ------------------------------------------
a {
  color: $text-primary;
  text-decoration: none;
  transition: color $transition-fast $ease-out;
  
  &:hover {
    color: $primary-color;
  }
  
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

// 列表
// ------------------------------------------
ul, ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

li {
  margin: 0;
  padding: 0;
}

// 图片
// ------------------------------------------
img {
  @include img-responsive;
  border: 0;
  vertical-align: middle;
}

// 表单元素
// ------------------------------------------
button,
input,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

button {
  background: transparent;
  border: 0;
  cursor: pointer;
  
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

input,
textarea {
  &:focus {
    outline: none;
  }
}

// 表格
// ------------------------------------------
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

th,
td {
  padding: $spacing-sm;
  text-align: left;
  vertical-align: top;
  border-bottom: 1px solid $border-light;
}

th {
  font-weight: $font-weight-semibold;
  background-color: $bg-secondary;
}

// 其他元素
// ------------------------------------------
hr {
  border: 0;
  height: 1px;
  background-color: $border-light;
  margin: $spacing-lg 0;
}

blockquote {
  margin: $spacing-lg 0;
  padding: $spacing-base $spacing-lg;
  border-left: 4px solid $primary-color;
  background-color: $bg-hover;
}

code {
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  padding: 2px 4px;
  background-color: $bg-hover;
  border-radius: $border-radius-sm;
}

pre {
  font-family: 'Courier New', monospace;
  background-color: $bg-hover;
  padding: $spacing-base;
  border-radius: $border-radius-base;
  overflow-x: auto;
  
  code {
    padding: 0;
    background: transparent;
  }
}

// 辅助类
// ------------------------------------------
.sr-only {
  @include visually-hidden;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: $primary-color;
}

.text-secondary {
  color: $text-secondary;
}

.text-muted {
  color: $text-tertiary;
}

.bg-primary {
  background-color: $primary-color;
}

.bg-secondary {
  background-color: $bg-secondary;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

.d-flex {
  display: flex;
}

.flex-center {
  @include flex-center;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

// 间距辅助类
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
