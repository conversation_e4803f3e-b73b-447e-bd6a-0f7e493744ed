<template>
  <div class="product-card" @click="handleClick">
    <!-- 商品图片区域 -->
    <div class="product-image">
      <img 
        :src="product.image" 
        :alt="product.title"
        class="product-img"
        @error="handleImageError"
      />
      
      <!-- 促销标签 -->
      <div class="promotion-tags" v-if="product.tags && product.tags.length">
        <span 
          class="promotion-tag" 
          v-for="tag in product.tags.slice(0, 2)" 
          :key="tag"
          :class="getTagClass(tag)"
        >
          {{ tag }}
        </span>
      </div>
    </div>
    
    <!-- 商品信息区域 -->
    <div class="product-info">
      <!-- 商品标题 -->
      <h3 class="product-title" :title="product.title">
        {{ product.title }}
      </h3>
      
      <!-- 价格区域 -->
      <div class="price-section">
        <div class="current-price">
          <span class="currency">¥</span>
          <span class="price">{{ formatPrice(product.price) }}</span>
        </div>
        <div class="original-price" v-if="product.originalPrice">
          ¥{{ formatPrice(product.originalPrice) }}
        </div>
      </div>
      
      <!-- 商品评价 -->
      <div class="product-rating" v-if="product.rating">
        <div class="stars">
          <span 
            class="star" 
            v-for="i in 5" 
            :key="i"
            :class="{ active: i <= Math.floor(product.rating) }"
          >
            ★
          </span>
        </div>
        <span class="rating-text">({{ product.reviewCount || 0 }})</span>
      </div>
      
      <!-- 店铺信息 -->
      <div class="shop-info" v-if="product.shop">
        <span class="shop-name">{{ product.shop }}</span>
      </div>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="product-actions">
      <button 
        class="btn btn-primary add-cart-btn" 
        @click.stop="handleAddToCart"
        :disabled="!product.inStock"
      >
        <i class="icon-cart"></i>
        {{ product.inStock ? '加入购物车' : '暂时缺货' }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductCard',
  props: {
    product: {
      type: Object,
      required: true,
      default: () => ({
        id: '',
        title: '',
        price: 0,
        originalPrice: 0,
        image: '',
        tags: [],
        rating: 0,
        reviewCount: 0,
        shop: '',
        inStock: true
      })
    }
  },
  emits: ['click', 'add-to-cart'],
  methods: {
    handleClick() {
      this.$emit('click', this.product)
    },
    
    handleAddToCart() {
      if (this.product.inStock) {
        this.$emit('add-to-cart', this.product)
      }
    },
    
    handleImageError(event) {
      // 图片加载失败时显示默认图片
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjI4MCIgdmlld0JveD0iMCAwIDI4MCAyODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyODAiIGhlaWdodD0iMjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xNDAgMTAwQzEyNS44NTggMTAwIDExNC4yODYgMTExLjU3MiAxMTQuMjg2IDEyNS43MTRDMTE0LjI4NiAxMzkuODU2IDEyNS44NTggMTUxLjQyOSAxNDAgMTUxLjQyOUMxNTQuMTQyIDE1MS40MjkgMTY1LjcxNCAxMzkuODU2IDE2NS43MTQgMTI1LjcxNEMxNjUuNzE0IDExMS41NzIgMTU0LjE0MiAxMDAgMTQwIDEwMFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPHBhdGggZD0iTTEwMCAxODBIMTgwVjE2MEgxMDBWMTgwWiIgZmlsbD0iI0NDQ0NDQyIvPgo8L3N2Zz4K'
    },
    
    formatPrice(price) {
      return Number(price).toFixed(2)
    },
    
    getTagClass(tag) {
      const tagMap = {
        '限时抢购': 'tag-sale',
        '新品': 'tag-new',
        '热销': 'tag-hot',
        '包邮': 'tag-free-shipping',
        '自营': 'tag-self-operated'
      }
      return tagMap[tag] || 'tag-default'
    }
  }
}
</script>

<style lang="scss" scoped>
.product-card {
  @include card-base;
  padding: $spacing-base;
  cursor: pointer;
  transition: all $transition-base $ease-out;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow-lg;
  }
  
  // 商品图片区域
  .product-image {
    position: relative;
    margin-bottom: $spacing-base;
    
    .product-img {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: $border-radius-base;
      background-color: $bg-hover;
    }
    
    .promotion-tags {
      position: absolute;
      top: $spacing-xs;
      left: $spacing-xs;
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;
      
      .promotion-tag {
        padding: 2px 6px;
        font-size: 10px;
        font-weight: $font-weight-medium;
        border-radius: $border-radius-sm;
        color: $bg-primary;
        
        &.tag-sale {
          background-color: $primary-color;
        }
        
        &.tag-new {
          background-color: $success-color;
        }
        
        &.tag-hot {
          background-color: $warning-color;
          color: $text-primary;
        }
        
        &.tag-free-shipping {
          background-color: $info-color;
        }
        
        &.tag-self-operated {
          background-color: $text-secondary;
        }
        
        &.tag-default {
          background-color: $text-tertiary;
        }
      }
    }
  }
  
  // 商品信息区域
  .product-info {
    flex: 1;
    margin-bottom: $spacing-base;
    
    .product-title {
      @include text-ellipsis-multiline(2);
      font-size: $font-size-sm;
      font-weight: $font-weight-normal;
      color: $text-primary;
      line-height: 1.4;
      margin-bottom: $spacing-sm;
      min-height: 2.8em;
    }
    
    .price-section {
      margin-bottom: $spacing-sm;
      
      .current-price {
        display: flex;
        align-items: baseline;
        margin-bottom: 2px;
        
        .currency {
          font-size: $font-size-sm;
          color: $primary-color;
          font-weight: $font-weight-medium;
        }
        
        .price {
          font-size: $font-size-xl;
          color: $primary-color;
          font-weight: $font-weight-bold;
        }
      }
      
      .original-price {
        font-size: $font-size-xs;
        color: $text-tertiary;
        text-decoration: line-through;
      }
    }
    
    .product-rating {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      margin-bottom: $spacing-xs;
      
      .stars {
        display: flex;
        
        .star {
          font-size: 12px;
          color: #ddd;
          
          &.active {
            color: $warning-color;
          }
        }
      }
      
      .rating-text {
        font-size: $font-size-xs;
        color: $text-tertiary;
      }
    }
    
    .shop-info {
      .shop-name {
        font-size: $font-size-xs;
        color: $text-secondary;
      }
    }
  }
  
  // 操作按钮区域
  .product-actions {
    .add-cart-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      font-size: $font-size-xs;
      padding: $spacing-xs $spacing-sm;
      
      .icon-cart {
        width: 14px;
        height: 14px;
        background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuMzMzMzMgMi4zMzMzM0gzLjE2NjY3TDQuNSA4Ljc1SDkuMzMzMzNMMTEuMDgzMyAzLjVINC4wODMzM000LjUgMTEuNjY2N0MzLjk5NSAxMS42NjY3IDMuNzMzMzMgMTIuMTI4MyAzLjczMzMzIDEyLjQ4MzNDMy43MzMzMyAxMi44MzgzIDMuOTk1IDEzLjMgNC41IDEzLjNDNS4wMDUgMTMuMyA1LjI2NjY3IDEyLjgzODMgNS4yNjY2NyAxMi40ODMzQzUuMjY2NjcgMTIuMTI4MyA1LjAwNSAxMS42NjY3IDQuNSAxMS42NjY3Wk05LjMzMzMzIDExLjY2NjdDOC44MjgzMyAxMS42NjY3IDguNTY2NjcgMTIuMTI4MyA4LjU2NjY3IDEyLjQ4MzNDOC41NjY2NyAxMi44MzgzIDguODI4MzMgMTMuMyA5LjMzMzMzIDEzLjNDOS44MzgzMyAxMy4zIDEwLjEgMTIuODM4MyAxMC4xIDEyLjQ4MzNDMTAuMSAxMi4xMjgzIDkuODM4MzMgMTEuNjY2NyA5LjMzMzMzIDExLjY2NjdaIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjEuMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPg==') no-repeat center;
      }
      
      &:disabled {
        background-color: $bg-disabled;
        color: $text-tertiary;
        cursor: not-allowed;
        
        .icon-cart {
          opacity: 0.5;
        }
      }
    }
  }
  
  // 移动端适配
  @include respond-to(xs) {
    .product-image .product-img {
      height: 150px;
    }
    
    .product-info .product-title {
      font-size: $font-size-xs;
      min-height: 2.4em;
    }
    
    .price-section .current-price .price {
      font-size: $font-size-lg;
    }
  }
}
</style>
