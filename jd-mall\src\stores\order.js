import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useOrderStore = defineStore('order', () => {
  // 状态
  const orders = ref([])
  const currentOrder = ref(null)
  const isLoading = ref(false)

  // 计算属性
  const ordersByStatus = computed(() => {
    const grouped = {
      all: orders.value,
      pending: orders.value.filter(order => order.status === 'pending'),
      paid: orders.value.filter(order => order.status === 'paid'),
      shipped: orders.value.filter(order => order.status === 'shipped'),
      delivered: orders.value.filter(order => order.status === 'delivered'),
      completed: orders.value.filter(order => order.status === 'completed'),
      cancelled: orders.value.filter(order => order.status === 'cancelled')
    }
    return grouped
  })

  const totalOrderCount = computed(() => orders.value.length)
  const totalOrderAmount = computed(() => {
    return orders.value.reduce((total, order) => total + order.totalAmount, 0)
  })

  // 动作
  const createOrder = async (orderData) => {
    isLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const newOrder = {
        id: `JD${Date.now()}${Math.floor(Math.random() * 1000)}`,
        userId: orderData.userId,
        items: orderData.items,
        shippingAddress: orderData.shippingAddress,
        paymentMethod: orderData.paymentMethod,
        totalAmount: orderData.totalAmount,
        shippingFee: orderData.shippingFee || 0,
        discountAmount: orderData.discountAmount || 0,
        status: 'pending',
        statusText: '待付款',
        createTime: new Date().toISOString(),
        paymentTime: null,
        shipTime: null,
        deliveryTime: null,
        completeTime: null,
        remark: orderData.remark || '',
        logistics: {
          company: '',
          trackingNumber: '',
          status: '',
          updates: []
        }
      }
      
      orders.value.unshift(newOrder)
      currentOrder.value = newOrder
      
      // 保存到本地存储
      saveToStorage()
      
      return { success: true, data: newOrder }
    } catch (error) {
      console.error('创建订单失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const payOrder = async (orderId, paymentData) => {
    isLoading.value = true
    try {
      // 模拟支付API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const order = orders.value.find(o => o.id === orderId)
      if (order) {
        order.status = 'paid'
        order.statusText = '已付款'
        order.paymentTime = new Date().toISOString()
        order.paymentMethod = paymentData.method
        
        // 模拟自动发货（实际项目中这应该是后台处理）
        setTimeout(() => {
          shipOrder(orderId)
        }, 5000)
        
        saveToStorage()
        return { success: true, data: order }
      }
      
      return { success: false, error: '订单不存在' }
    } catch (error) {
      console.error('支付订单失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const shipOrder = async (orderId) => {
    try {
      const order = orders.value.find(o => o.id === orderId)
      if (order && order.status === 'paid') {
        order.status = 'shipped'
        order.statusText = '已发货'
        order.shipTime = new Date().toISOString()
        order.logistics = {
          company: '京东物流',
          trackingNumber: `JDL${Date.now()}`,
          status: 'shipped',
          updates: [
            {
              time: new Date().toISOString(),
              status: '商品已发货',
              location: '北京分拣中心'
            }
          ]
        }
        
        // 模拟物流更新
        setTimeout(() => {
          updateLogistics(orderId)
        }, 10000)
        
        saveToStorage()
        return { success: true, data: order }
      }
      
      return { success: false, error: '订单状态不正确' }
    } catch (error) {
      console.error('发货失败:', error)
      return { success: false, error: error.message }
    }
  }

  const updateLogistics = (orderId) => {
    const order = orders.value.find(o => o.id === orderId)
    if (order && order.status === 'shipped') {
      const updates = [
        { status: '运输中', location: '上海转运中心' },
        { status: '派送中', location: '本地配送站' },
        { status: '已送达', location: '已签收' }
      ]
      
      let updateIndex = 0
      const interval = setInterval(() => {
        if (updateIndex < updates.length) {
          order.logistics.updates.push({
            time: new Date().toISOString(),
            ...updates[updateIndex]
          })
          
          if (updateIndex === updates.length - 1) {
            order.status = 'delivered'
            order.statusText = '已送达'
            order.deliveryTime = new Date().toISOString()
            clearInterval(interval)
            
            // 7天后自动完成订单
            setTimeout(() => {
              completeOrder(orderId)
            }, 7 * 24 * 60 * 60 * 1000)
          }
          
          updateIndex++
          saveToStorage()
        } else {
          clearInterval(interval)
        }
      }, 5000)
    }
  }

  const completeOrder = async (orderId) => {
    try {
      const order = orders.value.find(o => o.id === orderId)
      if (order && order.status === 'delivered') {
        order.status = 'completed'
        order.statusText = '已完成'
        order.completeTime = new Date().toISOString()
        
        saveToStorage()
        return { success: true, data: order }
      }
      
      return { success: false, error: '订单状态不正确' }
    } catch (error) {
      console.error('完成订单失败:', error)
      return { success: false, error: error.message }
    }
  }

  const cancelOrder = async (orderId, reason = '') => {
    isLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const order = orders.value.find(o => o.id === orderId)
      if (order && ['pending', 'paid'].includes(order.status)) {
        order.status = 'cancelled'
        order.statusText = '已取消'
        order.cancelTime = new Date().toISOString()
        order.cancelReason = reason
        
        saveToStorage()
        return { success: true, data: order }
      }
      
      return { success: false, error: '订单无法取消' }
    } catch (error) {
      console.error('取消订单失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const fetchOrders = async (params = {}) => {
    isLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 600))
      
      // 从本地存储加载订单
      loadFromStorage()
      
      return { success: true, data: orders.value }
    } catch (error) {
      console.error('获取订单列表失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const fetchOrderDetail = async (orderId) => {
    isLoading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 400))
      
      const order = orders.value.find(o => o.id === orderId)
      if (order) {
        currentOrder.value = order
        return { success: true, data: order }
      }
      
      return { success: false, error: '订单不存在' }
    } catch (error) {
      console.error('获取订单详情失败:', error)
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  const getOrderById = (orderId) => {
    return orders.value.find(o => o.id === orderId)
  }

  const saveToStorage = () => {
    try {
      localStorage.setItem('user_orders', JSON.stringify(orders.value))
    } catch (error) {
      console.error('保存订单数据失败:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const savedOrders = localStorage.getItem('user_orders')
      if (savedOrders) {
        orders.value = JSON.parse(savedOrders)
      }
    } catch (error) {
      console.error('加载订单数据失败:', error)
      orders.value = []
    }
  }

  const clearOrders = () => {
    orders.value = []
    currentOrder.value = null
    saveToStorage()
  }

  return {
    // 状态
    orders,
    currentOrder,
    isLoading,
    
    // 计算属性
    ordersByStatus,
    totalOrderCount,
    totalOrderAmount,
    
    // 动作
    createOrder,
    payOrder,
    shipOrder,
    completeOrder,
    cancelOrder,
    fetchOrders,
    fetchOrderDetail,
    getOrderById,
    saveToStorage,
    loadFromStorage,
    clearOrders
  }
})
