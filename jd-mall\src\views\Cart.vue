<template>
  <div class="cart-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">购物车</h1>
        <div class="cart-count">共 {{ itemCount }} 件商品</div>
      </div>

      <!-- 购物车为空 -->
      <div v-if="isEmpty" class="empty-cart">
        <div class="empty-icon">🛒</div>
        <h3 class="empty-title">购物车是空的</h3>
        <p class="empty-description">快去挑选心仪的商品吧</p>
        <button class="btn btn-primary" @click="$router.push('/')">
          去购物
        </button>
      </div>

      <!-- 购物车内容 -->
      <div v-else class="cart-content">
        <!-- 购物车头部 -->
        <div class="cart-header">
          <div class="select-all">
            <label class="checkbox-wrapper">
              <input 
                type="checkbox" 
                :checked="isAllSelected"
                @change="handleSelectAll"
              />
              <span class="checkmark"></span>
              <span class="label-text">全选</span>
            </label>
          </div>
          <div class="header-actions">
            <button 
              class="btn btn-text"
              @click="clearSelected"
              :disabled="selectedCount === 0"
            >
              删除选中商品
            </button>
          </div>
        </div>

        <!-- 购物车商品列表 -->
        <div class="cart-items">
          <div 
            class="cart-item"
            v-for="item in items"
            :key="`${item.id}-${JSON.stringify(item.selectedSpec)}`"
          >
            <div class="item-select">
              <label class="checkbox-wrapper">
                <input 
                  type="checkbox" 
                  :checked="item.selected"
                  @change="toggleItemSelection(item)"
                />
                <span class="checkmark"></span>
              </label>
            </div>

            <div class="item-image">
              <img 
                :src="item.image" 
                :alt="item.title"
                @click="goToProduct(item.id)"
              />
            </div>

            <div class="item-info">
              <h3 class="item-title" @click="goToProduct(item.id)">
                {{ item.title }}
              </h3>
              
              <div class="item-specs" v-if="item.selectedSpec">
                <span 
                  class="spec-tag"
                  v-for="(value, key) in item.selectedSpec"
                  :key="key"
                >
                  {{ key }}: {{ value }}
                </span>
              </div>
              
              <div class="item-shop">
                <span class="shop-name">{{ item.shop }}</span>
              </div>
            </div>

            <div class="item-price">
              <div class="current-price">¥{{ formatPrice(item.price) }}</div>
              <div class="original-price" v-if="item.originalPrice">
                ¥{{ formatPrice(item.originalPrice) }}
              </div>
            </div>

            <div class="item-quantity">
              <div class="quantity-control">
                <button 
                  class="quantity-btn decrease"
                  @click="decreaseQuantity(item)"
                  :disabled="item.quantity <= 1"
                >
                  -
                </button>
                <input 
                  type="number" 
                  class="quantity-input"
                  :value="item.quantity"
                  @input="updateQuantity(item, $event.target.value)"
                  :min="1"
                  :max="item.maxQuantity || 99"
                />
                <button 
                  class="quantity-btn increase"
                  @click="increaseQuantity(item)"
                  :disabled="item.quantity >= (item.maxQuantity || 99)"
                >
                  +
                </button>
              </div>
            </div>

            <div class="item-total">
              ¥{{ formatPrice(item.price * item.quantity) }}
            </div>

            <div class="item-actions">
              <button 
                class="btn btn-text remove-btn"
                @click="removeItem(item)"
              >
                删除
              </button>
            </div>
          </div>
        </div>

        <!-- 购物车底部结算 -->
        <div class="cart-footer">
          <div class="footer-left">
            <div class="select-all">
              <label class="checkbox-wrapper">
                <input 
                  type="checkbox" 
                  :checked="isAllSelected"
                  @change="handleSelectAll"
                />
                <span class="checkmark"></span>
                <span class="label-text">全选</span>
              </label>
            </div>
            <div class="selected-info">
              已选择 {{ selectedCount }} 件商品
            </div>
          </div>

          <div class="footer-right">
            <div class="price-summary">
              <div class="total-price">
                <span class="label">合计：</span>
                <span class="price">¥{{ formatPrice(selectedTotalPrice) }}</span>
              </div>
              <div class="discount-info" v-if="discountAmount > 0">
                已优惠：¥{{ formatPrice(discountAmount) }}
              </div>
            </div>
            
            <button 
              class="btn btn-primary checkout-btn"
              @click="goToCheckout"
              :disabled="selectedCount === 0"
            >
              结算 ({{ selectedCount }})
            </button>
          </div>
        </div>
      </div>

      <!-- 推荐商品 -->
      <div class="recommended-products" v-if="!isEmpty">
        <h3 class="section-title">为你推荐</h3>
        <div class="products-grid">
          <ProductCard
            v-for="product in recommendedProducts"
            :key="product.id"
            :product="product"
            @click="goToProduct"
            @add-to-cart="handleAddToCart"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart.js'
import { useProductStore } from '../stores/product.js'
import ProductCard from '../components/common/ProductCard.vue'

export default {
  name: 'Cart',
  components: {
    ProductCard
  },
  setup() {
    const router = useRouter()
    const cartStore = useCartStore()
    const productStore = useProductStore()

    // 计算属性
    const items = computed(() => cartStore.items)
    const itemCount = computed(() => cartStore.itemCount)
    const selectedCount = computed(() => cartStore.selectedCount)
    const selectedTotalPrice = computed(() => cartStore.selectedTotalPrice)
    const isEmpty = computed(() => cartStore.isEmpty)
    const recommendedProducts = computed(() => productStore.recommendProducts.slice(0, 4))

    const isAllSelected = computed(() => {
      return items.value.length > 0 && items.value.every(item => item.selected)
    })

    const discountAmount = computed(() => {
      // 模拟优惠金额计算
      return selectedTotalPrice.value > 200 ? 20 : 0
    })

    // 方法
    const handleSelectAll = (event) => {
      cartStore.selectAll(event.target.checked)
    }

    const toggleItemSelection = (item) => {
      cartStore.toggleItemSelection(item.id, item.selectedSpec)
    }

    const updateQuantity = (item, quantity) => {
      const newQuantity = parseInt(quantity) || 1
      cartStore.updateQuantity(item.id, newQuantity, item.selectedSpec)
    }

    const increaseQuantity = (item) => {
      cartStore.updateQuantity(item.id, item.quantity + 1, item.selectedSpec)
    }

    const decreaseQuantity = (item) => {
      cartStore.updateQuantity(item.id, item.quantity - 1, item.selectedSpec)
    }

    const removeItem = (item) => {
      if (confirm('确定要删除这件商品吗？')) {
        cartStore.removeFromCart(item.id, item.selectedSpec)
      }
    }

    const clearSelected = () => {
      if (confirm(`确定要删除选中的 ${selectedCount.value} 件商品吗？`)) {
        cartStore.clearSelected()
      }
    }

    const goToProduct = (productId) => {
      router.push(`/product/${productId}`)
    }

    const goToCheckout = () => {
      if (selectedCount.value === 0) {
        alert('请选择要结算的商品')
        return
      }
      router.push('/checkout')
    }

    const handleAddToCart = (product) => {
      cartStore.addToCart(product, 1)
    }

    const formatPrice = (price) => {
      return Number(price).toFixed(2)
    }

    // 组件挂载时验证购物车
    onMounted(async () => {
      await cartStore.validateCart()
    })

    return {
      items,
      itemCount,
      selectedCount,
      selectedTotalPrice,
      isEmpty,
      recommendedProducts,
      isAllSelected,
      discountAmount,
      handleSelectAll,
      toggleItemSelection,
      updateQuantity,
      increaseQuantity,
      decreaseQuantity,
      removeItem,
      clearSelected,
      goToProduct,
      goToCheckout,
      handleAddToCart,
      formatPrice
    }
  }
}
</script>

<style lang="scss" scoped>
.cart-page {
  min-height: 100vh;
  background-color: $bg-secondary;
  padding: $spacing-6 0;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-6;

    .page-title {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
    }

    .cart-count {
      color: $text-secondary;
      font-size: $font-size-base;
    }
  }

  // 空购物车
  .empty-cart {
    text-align: center;
    padding: $spacing-16 $spacing-6;
    background-color: $bg-primary;
    border-radius: $border-radius-lg;

    .empty-icon {
      font-size: 80px;
      margin-bottom: $spacing-6;
      opacity: 0.5;
    }

    .empty-title {
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-3;
    }

    .empty-description {
      color: $text-secondary;
      margin-bottom: $spacing-6;
    }
  }

  // 购物车内容
  .cart-content {
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    overflow: hidden;
    margin-bottom: $spacing-6;

    .cart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-4 $spacing-6;
      border-bottom: 1px solid $border-light;
      background-color: $bg-tertiary;

      .select-all {
        display: flex;
        align-items: center;
      }

      .header-actions {
        .btn {
          color: $text-secondary;

          &:hover:not(:disabled) {
            color: $primary-color;
          }

          &:disabled {
            color: $text-tertiary;
            cursor: not-allowed;
          }
        }
      }
    }

    .cart-items {
      .cart-item {
        display: grid;
        grid-template-columns: 50px 100px 1fr 120px 120px 100px 80px;
        gap: $spacing-4;
        align-items: center;
        padding: $spacing-4 $spacing-6;
        border-bottom: 1px solid $border-light;
        transition: background-color $transition-fast $ease-out;

        &:hover {
          background-color: $bg-hover;
        }

        &:last-child {
          border-bottom: none;
        }

        @include respond-to(md) {
          grid-template-columns: 1fr;
          gap: $spacing-3;
          padding: $spacing-4;
        }

        .item-select {
          display: flex;
          justify-content: center;

          @include respond-to(md) {
            justify-content: flex-start;
          }
        }

        .item-image {
          img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: $border-radius-base;
            cursor: pointer;
            transition: transform $transition-fast $ease-out;

            &:hover {
              transform: scale(1.05);
            }
          }
        }

        .item-info {
          .item-title {
            font-size: $font-size-base;
            font-weight: $font-weight-semibold;
            color: $text-primary;
            margin-bottom: $spacing-2;
            cursor: pointer;
            transition: color $transition-fast $ease-out;

            &:hover {
              color: $primary-color;
            }

            @include text-ellipsis-multiline(2);
          }

          .item-specs {
            display: flex;
            flex-wrap: wrap;
            gap: $spacing-1;
            margin-bottom: $spacing-2;

            .spec-tag {
              background-color: $bg-tertiary;
              color: $text-secondary;
              padding: $spacing-1 $spacing-2;
              border-radius: $border-radius-sm;
              font-size: $font-size-sm;
            }
          }

          .item-shop {
            .shop-name {
              color: $text-tertiary;
              font-size: $font-size-sm;
            }
          }
        }

        .item-price {
          text-align: right;

          @include respond-to(md) {
            text-align: left;
          }

          .current-price {
            font-size: $font-size-lg;
            font-weight: $font-weight-bold;
            color: $primary-color;
            margin-bottom: $spacing-1;
          }

          .original-price {
            font-size: $font-size-sm;
            color: $text-tertiary;
            text-decoration: line-through;
          }
        }

        .item-quantity {
          .quantity-control {
            display: flex;
            align-items: center;
            border: 1px solid $border-medium;
            border-radius: $border-radius-base;
            overflow: hidden;
            width: fit-content;

            @include respond-to(md) {
              width: 120px;
            }

            .quantity-btn {
              width: 32px;
              height: 32px;
              border: none;
              background-color: $bg-hover;
              color: $text-primary;
              cursor: pointer;
              transition: background-color $transition-fast $ease-out;

              &:hover:not(:disabled) {
                background-color: $primary-color;
                color: $text-white;
              }

              &:disabled {
                background-color: $bg-disabled;
                color: $text-tertiary;
                cursor: not-allowed;
              }
            }

            .quantity-input {
              width: 50px;
              height: 32px;
              border: none;
              text-align: center;
              font-size: $font-size-sm;

              &:focus {
                outline: none;
              }
            }
          }
        }

        .item-total {
          font-size: $font-size-lg;
          font-weight: $font-weight-bold;
          color: $primary-color;
          text-align: right;

          @include respond-to(md) {
            text-align: left;
          }
        }

        .item-actions {
          text-align: center;

          @include respond-to(md) {
            text-align: left;
          }

          .remove-btn {
            color: $text-secondary;
            font-size: $font-size-sm;

            &:hover {
              color: $error-color;
            }
          }
        }
      }
    }

    .cart-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-4 $spacing-6;
      background-color: $bg-tertiary;

      @include respond-to(md) {
        flex-direction: column;
        gap: $spacing-4;
        align-items: stretch;
      }

      .footer-left {
        display: flex;
        align-items: center;
        gap: $spacing-4;

        .selected-info {
          color: $text-secondary;
          font-size: $font-size-sm;
        }
      }

      .footer-right {
        display: flex;
        align-items: center;
        gap: $spacing-6;

        @include respond-to(md) {
          justify-content: space-between;
        }

        .price-summary {
          text-align: right;

          @include respond-to(md) {
            text-align: left;
          }

          .total-price {
            font-size: $font-size-lg;
            margin-bottom: $spacing-1;

            .label {
              color: $text-primary;
            }

            .price {
              font-size: $font-size-xl;
              font-weight: $font-weight-black;
              color: $primary-color;
              margin-left: $spacing-2;
            }
          }

          .discount-info {
            font-size: $font-size-sm;
            color: $success-color;
          }
        }

        .checkout-btn {
          padding: $spacing-3 $spacing-6;
          font-size: $font-size-base;
          font-weight: $font-weight-semibold;
          border-radius: $border-radius-lg;
          min-width: 120px;
        }
      }
    }
  }

  // 复选框样式
  .checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;

    input[type="checkbox"] {
      display: none;
    }

    .checkmark {
      width: 18px;
      height: 18px;
      border: 2px solid $border-medium;
      border-radius: $border-radius-sm;
      margin-right: $spacing-2;
      position: relative;
      transition: all $transition-fast $ease-out;

      &::after {
        content: '';
        position: absolute;
        left: 4px;
        top: 1px;
        width: 6px;
        height: 10px;
        border: solid $text-white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
        transition: opacity $transition-fast $ease-out;
      }
    }

    input[type="checkbox"]:checked + .checkmark {
      background-color: $primary-color;
      border-color: $primary-color;

      &::after {
        opacity: 1;
      }
    }

    .label-text {
      font-size: $font-size-base;
      color: $text-primary;
      user-select: none;
    }
  }

  // 推荐商品
  .recommended-products {
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    padding: $spacing-6;

    .section-title {
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-6;
      text-align: center;
    }

    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: $spacing-4;

      @include respond-to(sm) {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}
</style>
