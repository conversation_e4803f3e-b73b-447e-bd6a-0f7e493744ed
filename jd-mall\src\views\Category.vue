<template>
  <div class="category-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">商品分类</h1>
      </div>

      <div class="category-content">
        <!-- 分类导航 -->
        <div class="category-nav">
          <div 
            class="category-item"
            v-for="category in categories"
            :key="category.id"
            :class="{ active: selectedCategory?.id === category.id }"
            @click="selectCategory(category)"
          >
            <div class="category-icon">{{ category.icon }}</div>
            <div class="category-name">{{ category.name }}</div>
          </div>
        </div>

        <!-- 子分类 -->
        <div class="subcategory-nav" v-if="selectedCategory?.children">
          <button 
            class="subcategory-btn"
            :class="{ active: selectedSubcategory === null }"
            @click="selectedSubcategory = null"
          >
            全部
          </button>
          <button 
            class="subcategory-btn"
            v-for="subcategory in selectedCategory.children"
            :key="subcategory.id"
            :class="{ active: selectedSubcategory?.id === subcategory.id }"
            @click="selectedSubcategory = subcategory"
          >
            {{ subcategory.name }}
          </button>
        </div>

        <!-- 商品列表 -->
        <div class="products-section">
          <div class="section-header">
            <h3 class="section-title">
              {{ selectedCategory?.name || '全部商品' }}
              <span v-if="selectedSubcategory">- {{ selectedSubcategory.name }}</span>
            </h3>
            
            <div class="sort-options">
              <select v-model="sortBy" @change="applySorting">
                <option value="default">综合排序</option>
                <option value="price-asc">价格从低到高</option>
                <option value="price-desc">价格从高到低</option>
                <option value="sales">销量优先</option>
                <option value="rating">评分优先</option>
              </select>
            </div>
          </div>

          <ProductGrid 
            :products="filteredProducts"
            :loading="isLoading"
            @product-click="goToProduct"
            @add-to-cart="handleAddToCart"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductStore } from '../stores/product.js'
import { useCartStore } from '../stores/cart.js'
import ProductGrid from '../components/common/ProductGrid.vue'

export default {
  name: 'Category',
  components: {
    ProductGrid
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const productStore = useProductStore()
    const cartStore = useCartStore()

    const selectedCategory = ref(null)
    const selectedSubcategory = ref(null)
    const sortBy = ref('default')
    const isLoading = ref(false)

    const categories = computed(() => productStore.categories)
    const products = computed(() => productStore.products)

    const filteredProducts = computed(() => {
      let result = [...products.value]

      // 分类筛选
      if (selectedCategory.value) {
        result = result.filter(product => 
          product.categoryId === selectedCategory.value.id
        )
      }

      // 子分类筛选
      if (selectedSubcategory.value) {
        result = result.filter(product => 
          product.subcategoryId === selectedSubcategory.value.id
        )
      }

      // 排序
      switch (sortBy.value) {
        case 'price-asc':
          result.sort((a, b) => a.price - b.price)
          break
        case 'price-desc':
          result.sort((a, b) => b.price - a.price)
          break
        case 'sales':
          result.sort((a, b) => (b.sales || 0) - (a.sales || 0))
          break
        case 'rating':
          result.sort((a, b) => b.rating - a.rating)
          break
        default:
          // 默认排序
          break
      }

      return result
    })

    const selectCategory = (category) => {
      selectedCategory.value = category
      selectedSubcategory.value = null
      router.push(`/category/${category.id}`)
    }

    const applySorting = () => {
      // 排序逻辑已在计算属性中处理
    }

    const goToProduct = (product) => {
      router.push(`/product/${product.id}`)
    }

    const handleAddToCart = (product) => {
      cartStore.addToCart(product, 1)
    }

    onMounted(() => {
      const categoryId = route.params.id
      if (categoryId) {
        const category = categories.value.find(c => c.id == categoryId)
        if (category) {
          selectedCategory.value = category
        }
      }
    })

    return {
      selectedCategory,
      selectedSubcategory,
      sortBy,
      isLoading,
      categories,
      filteredProducts,
      selectCategory,
      applySorting,
      goToProduct,
      handleAddToCart
    }
  }
}
</script>

<style lang="scss" scoped>
.category-page {
  min-height: 100vh;
  background-color: $bg-secondary;
  padding: $spacing-6 0;
  
  .page-header {
    margin-bottom: $spacing-6;
    
    .page-title {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
    }
  }
  
  .category-content {
    .category-nav {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: $spacing-4;
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-6;
      margin-bottom: $spacing-6;
      
      @include respond-to(sm) {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: $spacing-2;
        padding: $spacing-4;
        border-radius: $border-radius-base;
        cursor: pointer;
        transition: all $transition-fast $ease-out;
        
        &:hover {
          background-color: $bg-hover;
        }
        
        &.active {
          background-color: $primary-light;
          color: $primary-color;
        }
        
        .category-icon {
          font-size: $font-size-xxl;
        }
        
        .category-name {
          font-weight: $font-weight-semibold;
          text-align: center;
        }
      }
    }
    
    .subcategory-nav {
      display: flex;
      gap: $spacing-2;
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-4 $spacing-6;
      margin-bottom: $spacing-6;
      overflow-x: auto;
      
      .subcategory-btn {
        padding: $spacing-2 $spacing-4;
        border: 1px solid $border-medium;
        border-radius: $border-radius-base;
        background-color: $bg-primary;
        color: $text-secondary;
        cursor: pointer;
        transition: all $transition-fast $ease-out;
        white-space: nowrap;
        
        &:hover {
          border-color: $primary-color;
          color: $primary-color;
        }
        
        &.active {
          background-color: $primary-color;
          border-color: $primary-color;
          color: $text-white;
        }
      }
    }
    
    .products-section {
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-6;
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-6;
        
        @include respond-to(sm) {
          flex-direction: column;
          gap: $spacing-3;
          align-items: flex-start;
        }
        
        .section-title {
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          color: $text-primary;
          margin: 0;
        }
        
        .sort-options {
          select {
            padding: $spacing-2 $spacing-3;
            border: 1px solid $border-medium;
            border-radius: $border-radius-base;
            background-color: $bg-primary;
            color: $text-primary;
            
            &:focus {
              outline: none;
              border-color: $primary-color;
            }
          }
        }
      }
    }
  }
}
</style>
