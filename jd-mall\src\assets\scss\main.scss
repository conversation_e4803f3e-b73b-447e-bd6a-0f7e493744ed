// 京东商城主样式文件
// ==========================================

// 导入基础样式
@import 'variables';
@import 'mixins';
@import 'reset';

// 全局容器样式
// ------------------------------------------
.container {
  @include make-container;
}

.container-fluid {
  width: 100%;
  padding: 0 $spacing-md;
  
  @include respond-to(lg) {
    padding: 0 $spacing-lg;
  }
}

// 网格系统
// ------------------------------------------
.row {
  @include make-row;
}

@for $i from 1 through 12 {
  .col-#{$i} {
    @include make-col($i);
  }
}

// 响应式网格
@include respond-to(sm) {
  @for $i from 1 through 12 {
    .col-sm-#{$i} {
      @include make-col($i);
    }
  }
}

@include respond-to(md) {
  @for $i from 1 through 12 {
    .col-md-#{$i} {
      @include make-col($i);
    }
  }
}

@include respond-to(lg) {
  @for $i from 1 through 12 {
    .col-lg-#{$i} {
      @include make-col($i);
    }
  }
}

// 通用组件样式
// ------------------------------------------

// 按钮组件
.btn {
  @include button-base;
  
  &.btn-primary {
    @include button-primary;
  }
  
  &.btn-secondary {
    @include button-secondary;
  }
  
  &.btn-sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
  }
  
  &.btn-lg {
    padding: $spacing-base $spacing-lg;
    font-size: $font-size-base;
  }
  
  &.btn-block {
    width: 100%;
    display: block;
  }
}

// 输入框组件
.form-control {
  @include input-base;
  
  &.form-control-sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
  }
  
  &.form-control-lg {
    padding: $spacing-base $spacing-md;
    font-size: $font-size-base;
  }
}

// 卡片组件
.card {
  @include card-base;
  
  .card-header {
    padding: $spacing-base $spacing-md;
    border-bottom: 1px solid $border-light;
    font-weight: $font-weight-semibold;
  }
  
  .card-body {
    padding: $spacing-md;
  }
  
  .card-footer {
    padding: $spacing-base $spacing-md;
    border-top: 1px solid $border-light;
    background-color: $bg-hover;
  }
}

// 徽标组件
.badge {
  display: inline-block;
  padding: $spacing-xs $spacing-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: $border-radius-base;
  
  &.badge-primary {
    background-color: $primary-color;
    color: $bg-primary;
  }
  
  &.badge-secondary {
    background-color: $text-tertiary;
    color: $bg-primary;
  }
  
  &.badge-success {
    background-color: $success-color;
    color: $bg-primary;
  }
  
  &.badge-warning {
    background-color: $warning-color;
    color: $text-primary;
  }
  
  &.badge-error {
    background-color: $error-color;
    color: $bg-primary;
  }
}

// 加载动画
// ------------------------------------------
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid $border-light;
  border-radius: 50%;
  border-top-color: $primary-color;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 骨架屏
.skeleton {
  background: linear-gradient(90deg, $bg-hover 25%, $bg-secondary 50%, $bg-hover 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 工具类
// ------------------------------------------
.clearfix {
  @include clearfix;
}

.text-ellipsis {
  @include text-ellipsis;
}

.text-ellipsis-2 {
  @include text-ellipsis-multiline(2);
}

.text-ellipsis-3 {
  @include text-ellipsis-multiline(3);
}

// 响应式显示/隐藏
.d-sm-none {
  @include respond-to(sm) {
    display: none !important;
  }
}

.d-md-none {
  @include respond-to(md) {
    display: none !important;
  }
}

.d-lg-none {
  @include respond-to(lg) {
    display: none !important;
  }
}

.d-sm-block {
  @include respond-to(sm) {
    display: block !important;
  }
}

.d-md-block {
  @include respond-to(md) {
    display: block !important;
  }
}

.d-lg-block {
  @include respond-to(lg) {
    display: block !important;
  }
}
