// 京东商城样式混合器
// ==========================================

// 响应式断点混合器
// ------------------------------------------
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
}

// 移动端优先的响应式
@mixin mobile-first($breakpoint) {
  @media (min-width: $breakpoint) {
    @content;
  }
}

// 桌面端优先的响应式
@mixin desktop-first($breakpoint) {
  @media (max-width: $breakpoint - 1px) {
    @content;
  }
}

// 文本截断
// ------------------------------------------
// 单行文本截断
@mixin text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 多行文本截断
@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 清除浮动
// ------------------------------------------
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 居中对齐
// ------------------------------------------
// 水平居中
@mixin center-horizontal {
  margin-left: auto;
  margin-right: auto;
}

// 垂直居中
@mixin center-vertical {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

// 水平垂直居中
@mixin center-both {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// Flexbox居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 按钮样式
// ------------------------------------------
@mixin button-base {
  display: inline-block;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: $border-radius-base;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-fast $ease-out;
  user-select: none;
  
  &:focus {
    outline: none;
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 主要按钮
@mixin button-primary {
  @include button-base;
  background-color: $primary-color;
  color: $bg-primary;
  
  &:hover:not(:disabled) {
    background-color: $primary-hover;
  }
  
  &:active:not(:disabled) {
    transform: translateY(1px);
  }
}

// 次要按钮
@mixin button-secondary {
  @include button-base;
  background-color: $bg-primary;
  color: $text-primary;
  border: 1px solid $border-light;
  
  &:hover:not(:disabled) {
    border-color: $primary-color;
    color: $primary-color;
  }
}

// 卡片样式
// ------------------------------------------
@mixin card-base {
  background-color: $bg-primary;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-sm;
  transition: box-shadow $transition-base $ease-out;
  
  &:hover {
    box-shadow: $box-shadow-hover;
  }
}

// 输入框样式
// ------------------------------------------
@mixin input-base {
  width: 100%;
  padding: $spacing-sm $spacing-base;
  border: 1px solid $border-light;
  border-radius: $border-radius-base;
  font-size: $font-size-sm;
  font-family: $font-family-base;
  transition: border-color $transition-fast $ease-out;
  
  &::placeholder {
    color: $text-placeholder;
  }
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
  
  &:disabled {
    background-color: $bg-disabled;
    cursor: not-allowed;
  }
}

// 网格系统
// ------------------------------------------
@mixin make-container($max-width: $container-max-width) {
  width: 100%;
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 $spacing-md;
  
  @include respond-to(lg) {
    padding: 0 $spacing-lg;
  }
}

// 网格行
@mixin make-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 (-$spacing-sm);
}

// 网格列
@mixin make-col($size, $columns: 12) {
  flex: 0 0 percentage($size / $columns);
  max-width: percentage($size / $columns);
  padding: 0 $spacing-sm;
}

// 隐藏元素
// ------------------------------------------
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// 图片响应式
// ------------------------------------------
@mixin img-responsive {
  max-width: 100%;
  height: auto;
}

// 滚动条样式
// ------------------------------------------
@mixin custom-scrollbar($width: 8px, $track-color: $bg-secondary, $thumb-color: $border-medium) {
  &::-webkit-scrollbar {
    width: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $border-radius-base;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}
