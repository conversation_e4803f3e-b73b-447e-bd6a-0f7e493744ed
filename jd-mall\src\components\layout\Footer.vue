<template>
  <footer class="main-footer">
    <!-- 服务保障区域 -->
    <div class="service-section">
      <div class="container">
        <div class="service-list">
          <div class="service-item">
            <i class="service-icon icon-delivery"></i>
            <div class="service-content">
              <h4>正品保障</h4>
              <p>正品保障，提供发票</p>
            </div>
          </div>
          <div class="service-item">
            <i class="service-icon icon-return"></i>
            <div class="service-content">
              <h4>急速物流</h4>
              <p>急速物流，急速送达</p>
            </div>
          </div>
          <div class="service-item">
            <i class="service-icon icon-service"></i>
            <div class="service-content">
              <h4>无忧售后</h4>
              <p>7天无理由退换货</p>
            </div>
          </div>
          <div class="service-item">
            <i class="service-icon icon-support"></i>
            <div class="service-content">
              <h4>特色服务</h4>
              <p>私人定制家电套餐</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主要链接区域 -->
    <div class="links-section">
      <div class="container">
        <div class="links-grid">
          <div class="link-column">
            <h5 class="column-title">购物指南</h5>
            <ul class="link-list">
              <li><a href="#">购物流程</a></li>
              <li><a href="#">会员介绍</a></li>
              <li><a href="#">生活旅行</a></li>
              <li><a href="#">常见问题</a></li>
              <li><a href="#">大家电</a></li>
              <li><a href="#">联系客服</a></li>
            </ul>
          </div>
          <div class="link-column">
            <h5 class="column-title">配送方式</h5>
            <ul class="link-list">
              <li><a href="#">上门自提</a></li>
              <li><a href="#">211限时达</a></li>
              <li><a href="#">配送服务查询</a></li>
              <li><a href="#">配送费收取标准</a></li>
              <li><a href="#">海外配送</a></li>
            </ul>
          </div>
          <div class="link-column">
            <h5 class="column-title">支付方式</h5>
            <ul class="link-list">
              <li><a href="#">货到付款</a></li>
              <li><a href="#">在线支付</a></li>
              <li><a href="#">分期付款</a></li>
              <li><a href="#">邮局汇款</a></li>
              <li><a href="#">公司转账</a></li>
            </ul>
          </div>
          <div class="link-column">
            <h5 class="column-title">售后服务</h5>
            <ul class="link-list">
              <li><a href="#">售后政策</a></li>
              <li><a href="#">价格保护</a></li>
              <li><a href="#">退款说明</a></li>
              <li><a href="#">返修/退换货</a></li>
              <li><a href="#">取消订单</a></li>
            </ul>
          </div>
          <div class="link-column">
            <h5 class="column-title">特色服务</h5>
            <ul class="link-list">
              <li><a href="#">夺宝岛</a></li>
              <li><a href="#">DIY装机</a></li>
              <li><a href="#">延保服务</a></li>
              <li><a href="#">京东E卡</a></li>
              <li><a href="#">京东通信</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 版权信息区域 -->
    <div class="copyright-section">
      <div class="container">
        <div class="copyright-content">
          <p class="copyright-text">
            Copyright © 2024 京东商城 版权所有 - 京ICP备11041704号-1
          </p>
          <div class="cert-links">
            <a href="#" class="cert-link">京公网安备</a>
            <a href="#" class="cert-link">可信网站</a>
            <a href="#" class="cert-link">诚信网站</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'Footer'
}
</script>

<style lang="scss" scoped>
.main-footer {
  background-color: $bg-primary;
  margin-top: auto;
  
  // 服务保障区域
  .service-section {
    padding: $spacing-xl 0;
    border-bottom: 1px solid $border-light;
    
    .service-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: $spacing-lg;
      
      @include respond-to(xs) {
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-base;
      }
    }
    
    .service-item {
      display: flex;
      align-items: center;
      gap: $spacing-base;
      
      .service-icon {
        width: 40px;
        height: 40px;
        background-color: $primary-light;
        border-radius: $border-radius-round;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        
        &.icon-delivery::before {
          content: '🚚';
          font-size: 20px;
        }
        
        &.icon-return::before {
          content: '⚡';
          font-size: 20px;
        }
        
        &.icon-service::before {
          content: '🛡️';
          font-size: 20px;
        }
        
        &.icon-support::before {
          content: '⭐';
          font-size: 20px;
        }
      }
      
      .service-content {
        h4 {
          font-size: $font-size-sm;
          font-weight: $font-weight-semibold;
          color: $text-primary;
          margin-bottom: 2px;
        }
        
        p {
          font-size: $font-size-xs;
          color: $text-secondary;
          margin: 0;
        }
      }
    }
  }
  
  // 主要链接区域
  .links-section {
    padding: $spacing-xl 0;
    background-color: $bg-hover;
    
    .links-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: $spacing-lg;
      
      @include respond-to(md) {
        grid-template-columns: repeat(3, 1fr);
      }
      
      @include respond-to(sm) {
        grid-template-columns: repeat(2, 1fr);
      }
      
      @include respond-to(xs) {
        grid-template-columns: 1fr;
      }
    }
    
    .link-column {
      .column-title {
        font-size: $font-size-sm;
        font-weight: $font-weight-semibold;
        color: $text-primary;
        margin-bottom: $spacing-base;
      }
      
      .link-list {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
          margin-bottom: $spacing-xs;
          
          a {
            font-size: $font-size-xs;
            color: $text-secondary;
            text-decoration: none;
            transition: color $transition-fast $ease-out;
            
            &:hover {
              color: $primary-color;
            }
          }
        }
      }
    }
  }
  
  // 版权信息区域
  .copyright-section {
    padding: $spacing-lg 0;
    background-color: $bg-secondary;
    
    .copyright-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      @include respond-to(sm) {
        flex-direction: column;
        gap: $spacing-base;
        text-align: center;
      }
      
      .copyright-text {
        font-size: $font-size-xs;
        color: $text-tertiary;
        margin: 0;
      }
      
      .cert-links {
        display: flex;
        gap: $spacing-base;
        
        .cert-link {
          font-size: $font-size-xs;
          color: $text-tertiary;
          text-decoration: none;
          
          &:hover {
            color: $primary-color;
          }
        }
      }
    }
  }
}
</style>
