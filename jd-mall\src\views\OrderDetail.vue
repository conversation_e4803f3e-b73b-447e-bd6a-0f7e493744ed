<template>
  <div class="order-detail-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">订单详情</h1>
        <button class="btn btn-secondary" @click="$router.back()">
          返回
        </button>
      </div>

      <div v-if="order" class="order-detail">
        <!-- 订单状态 -->
        <div class="order-status-section">
          <div class="status-info">
            <div class="status-icon">📦</div>
            <div class="status-text">
              <h3>{{ order.statusText }}</h3>
              <p v-if="order.status === 'pending'">请在24小时内完成支付</p>
              <p v-else-if="order.status === 'shipped'">商品正在配送中</p>
            </div>
          </div>
          
          <div class="order-actions" v-if="order.status === 'pending'">
            <button class="btn btn-primary" @click="payOrder">
              立即付款
            </button>
            <button class="btn btn-secondary" @click="cancelOrder">
              取消订单
            </button>
          </div>
        </div>

        <!-- 物流信息 -->
        <div class="logistics-section" v-if="order.logistics?.trackingNumber">
          <h3 class="section-title">物流信息</h3>
          <div class="logistics-info">
            <div class="logistics-header">
              <span>{{ order.logistics.company }}</span>
              <span>{{ order.logistics.trackingNumber }}</span>
            </div>
            <div class="logistics-timeline">
              <div 
                class="timeline-item"
                v-for="(update, index) in order.logistics.updates"
                :key="index"
              >
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                  <div class="update-status">{{ update.status }}</div>
                  <div class="update-location">{{ update.location }}</div>
                  <div class="update-time">{{ formatDate(update.time) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="products-section">
          <h3 class="section-title">商品信息</h3>
          <div class="products-list">
            <div 
              class="product-item"
              v-for="item in order.items"
              :key="item.id"
            >
              <img :src="item.image" :alt="item.title" class="product-image" />
              <div class="product-info">
                <h4 class="product-title">{{ item.title }}</h4>
                <div class="product-specs" v-if="item.selectedSpec">
                  <span 
                    class="spec-tag"
                    v-for="(value, key) in item.selectedSpec"
                    :key="key"
                  >
                    {{ key }}: {{ value }}
                  </span>
                </div>
              </div>
              <div class="product-price">¥{{ formatPrice(item.price) }}</div>
              <div class="product-quantity">×{{ item.quantity }}</div>
              <div class="product-total">¥{{ formatPrice(item.price * item.quantity) }}</div>
            </div>
          </div>
        </div>

        <!-- 收货信息 -->
        <div class="address-section">
          <h3 class="section-title">收货信息</h3>
          <div class="address-info">
            <div class="recipient">
              <span class="name">{{ order.shippingAddress.name }}</span>
              <span class="phone">{{ order.shippingAddress.phone }}</span>
            </div>
            <div class="address">
              {{ order.shippingAddress.province }} 
              {{ order.shippingAddress.city }} 
              {{ order.shippingAddress.district }} 
              {{ order.shippingAddress.detail }}
            </div>
          </div>
        </div>

        <!-- 订单信息 -->
        <div class="order-info-section">
          <h3 class="section-title">订单信息</h3>
          <div class="order-details">
            <div class="detail-item">
              <span class="label">订单编号：</span>
              <span class="value">{{ order.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">下单时间：</span>
              <span class="value">{{ formatDate(order.createTime) }}</span>
            </div>
            <div class="detail-item" v-if="order.paymentTime">
              <span class="label">付款时间：</span>
              <span class="value">{{ formatDate(order.paymentTime) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">支付方式：</span>
              <span class="value">{{ getPaymentMethodName(order.paymentMethod) }}</span>
            </div>
            <div class="detail-item" v-if="order.remark">
              <span class="label">订单备注：</span>
              <span class="value">{{ order.remark }}</span>
            </div>
          </div>
        </div>

        <!-- 费用明细 -->
        <div class="summary-section">
          <h3 class="section-title">费用明细</h3>
          <div class="summary-details">
            <div class="summary-item">
              <span class="label">商品总价：</span>
              <span class="value">¥{{ formatPrice(order.totalAmount - order.shippingFee + order.discountAmount) }}</span>
            </div>
            <div class="summary-item" v-if="order.shippingFee > 0">
              <span class="label">运费：</span>
              <span class="value">¥{{ formatPrice(order.shippingFee) }}</span>
            </div>
            <div class="summary-item" v-if="order.discountAmount > 0">
              <span class="label">优惠减免：</span>
              <span class="value discount">-¥{{ formatPrice(order.discountAmount) }}</span>
            </div>
            <div class="summary-item total">
              <span class="label">实付金额：</span>
              <span class="value">¥{{ formatPrice(order.totalAmount) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="order-not-found">
        <div class="not-found-icon">📋</div>
        <h3>订单不存在</h3>
        <p>抱歉，您访问的订单不存在</p>
        <button class="btn btn-primary" @click="$router.push('/orders')">
          查看我的订单
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOrderStore } from '../stores/order.js'

export default {
  name: 'OrderDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const orderStore = useOrderStore()

    const order = ref(null)

    const payOrder = async () => {
      const result = await orderStore.payOrder(order.value.id, {
        method: order.value.paymentMethod
      })
      if (result.success) {
        order.value = result.data
      }
    }

    const cancelOrder = async () => {
      if (confirm('确定要取消这个订单吗？')) {
        const result = await orderStore.cancelOrder(order.value.id, '用户主动取消')
        if (result.success) {
          order.value = result.data
        }
      }
    }

    const getPaymentMethodName = (method) => {
      const methods = {
        alipay: '支付宝',
        wechat: '微信支付',
        unionpay: '银联支付'
      }
      return methods[method] || method
    }

    const formatPrice = (price) => {
      return Number(price).toFixed(2)
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString('zh-CN')
    }

    onMounted(async () => {
      const result = await orderStore.fetchOrderDetail(route.params.id)
      if (result.success) {
        order.value = result.data
      }
    })

    return {
      order,
      payOrder,
      cancelOrder,
      getPaymentMethodName,
      formatPrice,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
.order-detail-page {
  min-height: 100vh;
  background-color: $bg-secondary;
  padding: $spacing-6 0;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-6;
    
    .page-title {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
    }
  }
  
  .order-detail {
    > div {
      background-color: $bg-primary;
      border-radius: $border-radius-lg;
      padding: $spacing-6;
      margin-bottom: $spacing-4;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .section-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-4;
      padding-bottom: $spacing-2;
      border-bottom: 1px solid $border-light;
    }
    
    // 订单状态
    .order-status-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      @include respond-to(sm) {
        flex-direction: column;
        gap: $spacing-4;
        align-items: stretch;
      }
      
      .status-info {
        display: flex;
        align-items: center;
        gap: $spacing-4;
        
        .status-icon {
          font-size: 48px;
        }
        
        .status-text {
          h3 {
            font-size: $font-size-xl;
            color: $primary-color;
            margin-bottom: $spacing-1;
          }
          
          p {
            color: $text-secondary;
            margin: 0;
          }
        }
      }
      
      .order-actions {
        display: flex;
        gap: $spacing-3;
        
        @include respond-to(sm) {
          justify-content: center;
        }
      }
    }
    
    // 物流信息
    .logistics-section {
      .logistics-info {
        .logistics-header {
          display: flex;
          justify-content: space-between;
          padding: $spacing-3 $spacing-4;
          background-color: $bg-tertiary;
          border-radius: $border-radius-base;
          margin-bottom: $spacing-4;
          
          span {
            font-weight: $font-weight-semibold;
            color: $text-primary;
          }
        }
        
        .logistics-timeline {
          .timeline-item {
            display: flex;
            gap: $spacing-3;
            margin-bottom: $spacing-4;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .timeline-dot {
              width: 12px;
              height: 12px;
              background-color: $primary-color;
              border-radius: $border-radius-round;
              margin-top: 6px;
              flex-shrink: 0;
            }
            
            .timeline-content {
              flex: 1;
              
              .update-status {
                font-weight: $font-weight-semibold;
                color: $text-primary;
                margin-bottom: $spacing-1;
              }
              
              .update-location {
                color: $text-secondary;
                margin-bottom: $spacing-1;
              }
              
              .update-time {
                color: $text-tertiary;
                font-size: $font-size-sm;
              }
            }
          }
        }
      }
    }
    
    // 其他样式与订单列表页类似
    .products-list,
    .order-details,
    .summary-details {
      // 复用之前的样式
    }
  }
}
</style>
