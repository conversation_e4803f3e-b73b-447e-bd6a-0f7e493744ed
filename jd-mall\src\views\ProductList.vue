<template>
  <div class="product-list-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">商品列表</h1>
      </div>

      <div class="list-content">
        <!-- 筛选器 -->
        <div class="filters-section">
          <div class="filter-group">
            <label>价格范围：</label>
            <select v-model="filters.priceRange" @change="applyFilters">
              <option value="all">不限</option>
              <option value="0-100">0-100元</option>
              <option value="100-500">100-500元</option>
              <option value="500-1000">500-1000元</option>
              <option value="1000+">1000元以上</option>
            </select>
          </div>
          
          <div class="filter-group">
            <label>排序方式：</label>
            <select v-model="filters.sortBy" @change="applyFilters">
              <option value="default">综合排序</option>
              <option value="price-asc">价格从低到高</option>
              <option value="price-desc">价格从高到低</option>
              <option value="sales">销量优先</option>
              <option value="rating">评分优先</option>
            </select>
          </div>
        </div>

        <!-- 商品网格 -->
        <ProductGrid 
          :products="filteredProducts"
          :loading="isLoading"
          @product-click="goToProduct"
          @add-to-cart="handleAddToCart"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProductStore } from '../stores/product.js'
import { useCartStore } from '../stores/cart.js'
import ProductGrid from '../components/common/ProductGrid.vue'

export default {
  name: 'ProductList',
  components: {
    ProductGrid
  },
  setup() {
    const router = useRouter()
    const productStore = useProductStore()
    const cartStore = useCartStore()

    const isLoading = ref(false)
    const filters = ref({
      priceRange: 'all',
      sortBy: 'default'
    })

    const filteredProducts = computed(() => productStore.filteredProducts)

    const applyFilters = () => {
      // 应用筛选器逻辑
      productStore.updateFilters(filters.value)
    }

    const goToProduct = (product) => {
      router.push(`/product/${product.id}`)
    }

    const handleAddToCart = (product) => {
      cartStore.addToCart(product, 1)
    }

    onMounted(() => {
      productStore.fetchProducts()
    })

    return {
      isLoading,
      filters,
      filteredProducts,
      applyFilters,
      goToProduct,
      handleAddToCart
    }
  }
}
</script>

<style lang="scss" scoped>
.product-list-page {
  min-height: 100vh;
  background-color: $bg-secondary;
  padding: $spacing-6 0;
  
  .page-header {
    margin-bottom: $spacing-6;
    
    .page-title {
      font-size: $font-size-xxl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin: 0;
    }
  }
  
  .list-content {
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    padding: $spacing-6;
    
    .filters-section {
      display: flex;
      gap: $spacing-6;
      margin-bottom: $spacing-6;
      padding-bottom: $spacing-4;
      border-bottom: 1px solid $border-light;
      
      @include respond-to(sm) {
        flex-direction: column;
        gap: $spacing-3;
      }
      
      .filter-group {
        display: flex;
        align-items: center;
        gap: $spacing-2;
        
        label {
          font-weight: $font-weight-semibold;
          color: $text-primary;
          white-space: nowrap;
        }
        
        select {
          padding: $spacing-2 $spacing-3;
          border: 1px solid $border-medium;
          border-radius: $border-radius-base;
          background-color: $bg-primary;
          color: $text-primary;
          
          &:focus {
            outline: none;
            border-color: $primary-color;
          }
        }
      }
    }
  }
}
</style>
