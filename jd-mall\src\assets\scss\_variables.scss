// 现代电商样式变量定义
// ==========================================

// 色彩系统 - 参考现代电商设计
// ------------------------------------------
// 主品牌色 - 更现代的橙红色系
$primary-color: #FF6B35;        // 主橙色
$primary-hover: #E55A2B;        // 深橙色（悬停状态）
$primary-light: #FFF4F0;        // 浅橙色背景
$primary-gradient: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);

// 辅助色系
$secondary-color: #4A90E2;      // 蓝色
$secondary-hover: #357ABD;      // 深蓝色
$secondary-light: #F0F7FF;      // 浅蓝色背景

// 文本颜色 - 更现代的层次
$text-primary: #1A1A1A;         // 主要文本（更深）
$text-secondary: #4A4A4A;       // 次要文本
$text-tertiary: #8A8A8A;        // 辅助文本
$text-placeholder: #BFBFBF;     // 占位符文本
$text-white: #FFFFFF;           // 白色文本

// 背景颜色 - 现代渐变和层次
$bg-primary: #FFFFFF;           // 主背景
$bg-secondary: #FAFAFA;         // 次要背景
$bg-tertiary: #F5F5F5;          // 第三层背景
$bg-hover: #F8F8F8;             // 悬停背景
$bg-disabled: #F0F0F0;          // 禁用背景
$bg-dark: #2C2C2C;              // 深色背景
$bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

// 边框颜色 - 更细腻的层次
$border-light: #F0F0F0;         // 极浅边框
$border-medium: #E0E0E0;        // 中等边框
$border-dark: #D0D0D0;          // 深色边框
$border-primary: #FF6B35;       // 主色边框

// 功能色彩 - 更现代的配色
$success-color: #00C851;        // 成功色（绿色）
$warning-color: #FFB74D;        // 警告色（橙色）
$error-color: #FF5252;          // 错误色（红色）
$info-color: #2196F3;           // 信息色（蓝色）

// 阴影色彩
$shadow-light: rgba(0, 0, 0, 0.04);
$shadow-medium: rgba(0, 0, 0, 0.08);
$shadow-dark: rgba(0, 0, 0, 0.12);
$shadow-primary: rgba(255, 107, 53, 0.3);

// 字体系统 - 现代化字体设计
// ------------------------------------------
// 字体族 - 更现代的字体栈
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
$font-family-number: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
$font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

// 字号 - 更精细的字号系统
$font-size-xs: 11px;            // 极小字
$font-size-sm: 12px;            // 小字
$font-size-base: 14px;          // 基础字号
$font-size-md: 16px;            // 中等字号
$font-size-lg: 18px;            // 大字号
$font-size-xl: 20px;            // 超大字号
$font-size-xxl: 24px;           // 标题字号
$font-size-xxxl: 32px;          // 大标题字号

// 行高 - 更好的阅读体验
$line-height-tight: 1.2;        // 紧凑行高
$line-height-normal: 1.4;       // 正常行高
$line-height-relaxed: 1.6;      // 宽松行高
$line-height-loose: 1.8;        // 很宽松行高

// 字重 - 现代字重系统
$font-weight-light: 300;        // 细体
$font-weight-normal: 400;       // 正常
$font-weight-medium: 500;       // 中等
$font-weight-semibold: 600;     // 半粗
$font-weight-bold: 700;         // 粗体
$font-weight-black: 900;        // 超粗体

// 间距系统 - 8px基础网格系统
// ------------------------------------------
$spacing-0: 0;                  // 无间距
$spacing-1: 4px;                // 极小间距
$spacing-2: 8px;                // 小间距
$spacing-3: 12px;               // 基础间距
$spacing-4: 16px;               // 中等间距
$spacing-5: 20px;               // 大间距
$spacing-6: 24px;               // 超大间距
$spacing-8: 32px;               // 极大间距
$spacing-10: 40px;              // 超极大间距
$spacing-12: 48px;              // 巨大间距
$spacing-16: 64px;              // 超巨大间距

// 兼容旧变量名
$spacing-xs: $spacing-1;
$spacing-sm: $spacing-2;
$spacing-base: $spacing-3;
$spacing-md: $spacing-4;
$spacing-lg: $spacing-5;
$spacing-xl: $spacing-6;
$spacing-xxl: $spacing-8;

// 圆角 - 现代化圆角系统
// ------------------------------------------
$border-radius-none: 0;         // 无圆角
$border-radius-sm: 4px;         // 小圆角
$border-radius-base: 8px;       // 基础圆角
$border-radius-md: 12px;        // 中等圆角
$border-radius-lg: 16px;        // 大圆角
$border-radius-xl: 20px;        // 超大圆角
$border-radius-2xl: 24px;       // 超超大圆角
$border-radius-full: 9999px;    // 完全圆角
$border-radius-round: 50%;      // 圆形

// 阴影 - 现代化阴影系统
// ------------------------------------------
$box-shadow-none: none;
$box-shadow-xs: 0 1px 2px 0 $shadow-light;
$box-shadow-sm: 0 1px 3px 0 $shadow-light, 0 1px 2px 0 $shadow-medium;
$box-shadow-base: 0 4px 6px -1px $shadow-light, 0 2px 4px -1px $shadow-medium;
$box-shadow-md: 0 10px 15px -3px $shadow-light, 0 4px 6px -2px $shadow-medium;
$box-shadow-lg: 0 20px 25px -5px $shadow-light, 0 10px 10px -5px $shadow-medium;
$box-shadow-xl: 0 25px 50px -12px $shadow-dark;
$box-shadow-2xl: 0 25px 50px -12px $shadow-dark;
$box-shadow-inner: inset 0 2px 4px 0 $shadow-medium;
$box-shadow-hover: 0 8px 25px -8px $shadow-primary;
$box-shadow-focus: 0 0 0 3px $shadow-primary;

// 响应式断点
// ------------------------------------------
$breakpoint-xs: 480px;          // 超小屏
$breakpoint-sm: 768px;          // 小屏（平板）
$breakpoint-md: 992px;          // 中屏
$breakpoint-lg: 1200px;         // 大屏
$breakpoint-xl: 1400px;         // 超大屏

// 布局尺寸
// ------------------------------------------
$container-max-width: 1210px;   // 主容器最大宽度
$header-height: 80px;           // 主导航高度
$top-bar-height: 30px;          // 顶部通栏高度
$sidebar-width: 200px;          // 侧边栏宽度

// 动画时间
// ------------------------------------------
$transition-fast: 0.15s;        // 快速过渡
$transition-base: 0.3s;         // 基础过渡
$transition-slow: 0.5s;         // 慢速过渡

// 动画缓动函数
$ease-out: ease-out;
$ease-in-out: ease-in-out;
$ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// Z-index层级
// ------------------------------------------
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
