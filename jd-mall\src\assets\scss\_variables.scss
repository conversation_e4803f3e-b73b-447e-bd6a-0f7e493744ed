// 京东商城样式变量定义
// ==========================================

// 色彩系统
// ------------------------------------------
// 主品牌色
$primary-color: #E1251B;        // 京东红
$primary-hover: #C81623;        // 深红色（悬停状态）
$primary-light: #FFE8E8;        // 浅红色背景

// 文本颜色
$text-primary: #333333;         // 主要文本
$text-secondary: #666666;       // 次要文本
$text-tertiary: #999999;        // 辅助文本
$text-placeholder: #CCCCCC;     // 占位符文本

// 背景颜色
$bg-primary: #FFFFFF;           // 主背景
$bg-secondary: #F5F5F5;         // 次要背景
$bg-hover: #F8F8F8;             // 悬停背景
$bg-disabled: #F0F0F0;          // 禁用背景

// 边框颜色
$border-light: #E5E5E5;         // 浅色边框
$border-medium: #DDDDDD;        // 中等边框
$border-dark: #CCCCCC;          // 深色边框

// 功能色彩
$success-color: #52C41A;        // 成功色
$warning-color: #FAAD14;        // 警告色
$error-color: #FF4D4F;          // 错误色
$info-color: #1890FF;           // 信息色

// 字体系统
// ------------------------------------------
// 字体族
$font-family-base: 'Microsoft YaHei', '微软雅黑', Arial, Helvetica, sans-serif;
$font-family-number: Arial, Helvetica, sans-serif;

// 字号
$font-size-xs: 12px;            // 小字
$font-size-sm: 14px;            // 正文
$font-size-base: 16px;          // 基础字号
$font-size-lg: 18px;            // 二级标题
$font-size-xl: 20px;            // 价格、重要信息
$font-size-xxl: 24px;           // 主标题

// 行高
$line-height-xs: 16px;          // 小字行高
$line-height-sm: 20px;          // 正文行高
$line-height-base: 24px;        // 基础行高
$line-height-lg: 28px;          // 大字行高
$line-height-xl: 32px;          // 标题行高

// 字重
$font-weight-normal: 400;       // 正常
$font-weight-medium: 500;       // 中等
$font-weight-semibold: 600;     // 半粗
$font-weight-bold: 700;         // 粗体

// 间距系统
// ------------------------------------------
$spacing-xs: 4px;               // 极小间距
$spacing-sm: 8px;               // 小间距
$spacing-base: 12px;            // 基础间距
$spacing-md: 16px;              // 中等间距
$spacing-lg: 20px;              // 大间距
$spacing-xl: 24px;              // 超大间距
$spacing-xxl: 32px;             // 极大间距

// 圆角
// ------------------------------------------
$border-radius-sm: 2px;         // 小圆角
$border-radius-base: 4px;       // 基础圆角
$border-radius-lg: 6px;         // 大圆角
$border-radius-xl: 8px;         // 超大圆角
$border-radius-round: 50%;      // 圆形

// 阴影
// ------------------------------------------
$box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.1);
$box-shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);

// 响应式断点
// ------------------------------------------
$breakpoint-xs: 480px;          // 超小屏
$breakpoint-sm: 768px;          // 小屏（平板）
$breakpoint-md: 992px;          // 中屏
$breakpoint-lg: 1200px;         // 大屏
$breakpoint-xl: 1400px;         // 超大屏

// 布局尺寸
// ------------------------------------------
$container-max-width: 1210px;   // 主容器最大宽度
$header-height: 80px;           // 主导航高度
$top-bar-height: 30px;          // 顶部通栏高度
$sidebar-width: 200px;          // 侧边栏宽度

// 动画时间
// ------------------------------------------
$transition-fast: 0.15s;        // 快速过渡
$transition-base: 0.3s;         // 基础过渡
$transition-slow: 0.5s;         // 慢速过渡

// 动画缓动函数
$ease-out: ease-out;
$ease-in-out: ease-in-out;
$ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// Z-index层级
// ------------------------------------------
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
