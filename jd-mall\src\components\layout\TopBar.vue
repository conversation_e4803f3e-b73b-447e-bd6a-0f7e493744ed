<template>
  <div class="top-bar">
    <div class="container">
      <div class="top-bar-content">
        <!-- 左侧地区选择 -->
        <div class="location">
          <i class="icon-location"></i>
          <span>北京</span>
        </div>
        
        <!-- 右侧导航链接 -->
        <nav class="top-nav">
          <router-link to="/login" class="nav-link">你好，请登录</router-link>
          <router-link to="/register" class="nav-link">免费注册</router-link>
          <span class="divider">|</span>
          <router-link to="/orders" class="nav-link">我的订单</router-link>
          <span class="divider">|</span>
          <router-link to="/profile" class="nav-link">我的京东</router-link>
          <span class="divider">|</span>
          <a href="#" class="nav-link">京东会员</a>
          <span class="divider">|</span>
          <a href="#" class="nav-link">企业采购</a>
          <span class="divider">|</span>
          <a href="#" class="nav-link">客户服务</a>
          <span class="divider">|</span>
          <a href="#" class="nav-link">网站导航</a>
          <span class="divider">|</span>
          <a href="#" class="nav-link">手机京东</a>
        </nav>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TopBar'
}
</script>

<style lang="scss" scoped>
.top-bar {
  height: $top-bar-height;
  background-color: #f1f1f1;
  font-size: $font-size-xs;
  line-height: $top-bar-height;
  
  .top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
  }
  
  .location {
    display: flex;
    align-items: center;
    color: $text-secondary;
    
    .icon-location {
      width: 12px;
      height: 12px;
      margin-right: 4px;
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYgMUMzLjc5IDEgMiAyLjc5IDIgNUMyIDcuNSA2IDExIDYgMTFTMTAgNy41IDEwIDVDMTAgMi43OSA4LjIxIDEgNiAxWk02IDYuNUM1LjE3IDYuNSA0LjUgNS44MyA0LjUgNUM0LjUgNC4xNyA1LjE3IDMuNSA2IDMuNUM2LjgzIDMuNSA3LjUgNC4xNyA3LjUgNUM3LjUgNS44MyA2LjgzIDYuNSA2IDYuNVoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+') no-repeat center;
    }
  }
  
  .top-nav {
    display: flex;
    align-items: center;
    
    .nav-link {
      color: $text-secondary;
      font-size: $font-size-xs;
      text-decoration: none;
      transition: color $transition-fast $ease-out;
      
      &:hover {
        color: $primary-color;
      }
    }
    
    .divider {
      margin: 0 8px;
      color: #ccc;
    }
  }
  
  // 移动端隐藏
  @include respond-to(xs) {
    display: none;
  }
}
</style>
